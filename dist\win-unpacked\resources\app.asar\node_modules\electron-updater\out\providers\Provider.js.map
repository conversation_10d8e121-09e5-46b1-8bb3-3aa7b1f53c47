{"version": 3, "file": "Provider.js", "sourceRoot": "", "sources": ["../../src/providers/Provider.ts"], "names": [], "mappings": ";;;AAkFA,4BAaC;AAED,0CAeC;AAED,kCAmBC;AAED,oCAqBC;AA5JD,+DAAyJ;AAEzJ,qCAA8B;AAI9B,kCAAwC;AAWxC,MAAsB,QAAQ;IAI5B,YAAuC,cAAsC;QAAtC,mBAAc,GAAd,cAAc,CAAwB;QAHrE,mBAAc,GAA+B,IAAI,CAAA;QAIvD,IAAI,CAAC,QAAQ,GAAG,cAAc,CAAC,QAAQ,CAAA;IACzC,CAAC;IAED,IAAI,yBAAyB;QAC3B,OAAO,IAAI,CAAC,cAAc,CAAC,yBAAyB,KAAK,KAAK,CAAA;IAChE,CAAC;IAEO,oBAAoB;QAC1B,IAAI,IAAI,CAAC,cAAc,CAAC,QAAQ,KAAK,OAAO,EAAE,CAAC;YAC7C,MAAM,IAAI,GAAG,OAAO,CAAC,GAAG,CAAC,mBAAmB,CAAC,IAAI,OAAO,CAAC,IAAI,CAAA;YAC7D,MAAM,UAAU,GAAG,IAAI,KAAK,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE,CAAA;YACnD,OAAO,QAAQ,GAAG,UAAU,CAAA;QAC9B,CAAC;aAAM,CAAC;YACN,OAAO,IAAI,CAAC,cAAc,CAAC,QAAQ,KAAK,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAA;QAChE,CAAC;IACH,CAAC;IAED,uFAAuF;IAC7E,qBAAqB;QAC7B,OAAO,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAA;IAC5C,CAAC;IAES,oBAAoB,CAAC,OAAe;QAC5C,OAAO,GAAG,OAAO,GAAG,IAAI,CAAC,oBAAoB,EAAE,EAAE,CAAA;IACnD,CAAC;IAED,IAAI,wBAAwB;QAC1B,OAAO,IAAI,CAAA;IACb,CAAC;IAED,iBAAiB,CAAC,KAAiC;QACjD,IAAI,CAAC,cAAc,GAAG,KAAK,CAAA;IAC7B,CAAC;IAMD;;OAEG;IACO,WAAW,CAAC,GAAQ,EAAE,OAAoC,EAAE,iBAAqC;QACzG,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,oBAAoB,CAAC,GAAG,EAAE,OAAO,CAAC,EAAE,iBAAiB,CAAC,CAAA;IAC1F,CAAC;IAES,oBAAoB,CAAC,GAAQ,EAAE,OAAoC;QAC3E,MAAM,MAAM,GAAmB,EAAE,CAAA;QACjC,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,EAAE,CAAC;YAChC,IAAI,OAAO,IAAI,IAAI,EAAE,CAAC;gBACpB,MAAM,CAAC,OAAO,GAAG,OAAO,CAAA;YAC1B,CAAC;QACH,CAAC;aAAM,CAAC;YACN,MAAM,CAAC,OAAO,GAAG,OAAO,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,cAAc,EAAE,GAAG,OAAO,EAAE,CAAA;QACjG,CAAC;QAED,IAAA,0CAAmB,EAAC,GAAG,EAAE,MAAM,CAAC,CAAA;QAChC,OAAO,MAAM,CAAA;IACf,CAAC;CACF;AA/DD,4BA+DC;AAED,SAAgB,QAAQ,CAAC,KAAoC,EAAE,SAAiB,EAAE,GAAmB;IACnG,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QACvB,MAAM,IAAA,+BAAQ,EAAC,mBAAmB,EAAE,+BAA+B,CAAC,CAAA;IACtE,CAAC;IAED,MAAM,MAAM,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,IAAI,SAAS,EAAE,CAAC,CAAC,CAAA;IACxF,IAAI,MAAM,IAAI,IAAI,EAAE,CAAC;QACnB,OAAO,MAAM,CAAA;IACf,CAAC;SAAM,IAAI,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,OAAO,KAAK,CAAC,CAAC,CAAC,CAAA;IACjB,CAAC;SAAM,CAAC;QACN,OAAO,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,IAAI,GAAG,EAAE,CAAC,CAAC,CAAC,CAAA;IAC1G,CAAC;AACH,CAAC;AAED,SAAgB,eAAe,CAAC,OAAsB,EAAE,WAAmB,EAAE,cAAmB;IAC9F,IAAI,OAAO,IAAI,IAAI,EAAE,CAAC;QACpB,MAAM,IAAA,+BAAQ,EAAC,iCAAiC,WAAW,qCAAqC,cAAc,kBAAkB,EAAE,iCAAiC,CAAC,CAAA;IACtK,CAAC;IAED,IAAI,MAAkB,CAAA;IACtB,IAAI,CAAC;QACH,MAAM,GAAG,IAAA,cAAI,EAAC,OAAO,CAAe,CAAA;IACtC,CAAC;IAAC,OAAO,CAAM,EAAE,CAAC;QAChB,MAAM,IAAA,+BAAQ,EACZ,iCAAiC,WAAW,qCAAqC,cAAc,MAAM,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,OAAO,cAAc,OAAO,EAAE,EAChJ,iCAAiC,CAClC,CAAA;IACH,CAAC;IACD,OAAO,MAAM,CAAA;AACf,CAAC;AAED,SAAgB,WAAW,CAAC,UAAsB;IAChD,MAAM,KAAK,GAAG,UAAU,CAAC,KAAK,CAAA;IAC9B,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACtC,OAAO,KAAK,CAAA;IACd,CAAC;IAED,mCAAmC;IACnC,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,EAAE,CAAC;QAC5B,mCAAmC;QACnC,OAAO;YACL;gBACE,GAAG,EAAE,UAAU,CAAC,IAAI;gBACpB,IAAI,EAAG,UAAkB,CAAC,IAAI;gBAC9B,MAAM,EAAE,UAAU,CAAC,MAAM;aACnB;SACT,CAAA;IACH,CAAC;SAAM,CAAC;QACN,MAAM,IAAA,+BAAQ,EAAC,sBAAsB,IAAA,wCAAiB,EAAC,UAAU,CAAC,EAAE,EAAE,+BAA+B,CAAC,CAAA;IACxG,CAAC;AACH,CAAC;AAED,SAAgB,YAAY,CAAC,UAAsB,EAAE,OAAY,EAAE,kBAAyC,CAAC,CAAS,EAAU,EAAE,CAAC,CAAC;IAClI,MAAM,KAAK,GAAG,WAAW,CAAC,UAAU,CAAC,CAAA;IACrC,MAAM,MAAM,GAAkC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;QACjE,IAAK,QAAgB,CAAC,IAAI,IAAI,IAAI,IAAI,QAAQ,CAAC,MAAM,IAAI,IAAI,EAAE,CAAC;YAC9D,MAAM,IAAA,+BAAQ,EAAC,mEAAmE,IAAA,wCAAiB,EAAC,QAAQ,CAAC,EAAE,EAAE,yBAAyB,CAAC,CAAA;QAC7I,CAAC;QACD,OAAO;YACL,GAAG,EAAE,IAAA,qBAAc,EAAC,eAAe,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,OAAO,CAAC;YAC3D,IAAI,EAAE,QAAQ;SACf,CAAA;IACH,CAAC,CAAC,CAAA;IAEF,MAAM,QAAQ,GAAI,UAAgC,CAAC,QAAQ,CAAA;IAC3D,MAAM,WAAW,GAAG,QAAQ,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,QAAQ,CAAC,IAAI,CAAA;IACrF,IAAI,WAAW,IAAI,IAAI,EAAE,CAAC;QACxB,CAAC;QAAC,MAAM,CAAC,CAAC,CAAS,CAAC,WAAW,GAAG;YAChC,GAAG,WAAW;YACd,IAAI,EAAE,IAAA,qBAAc,EAAC,eAAe,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,OAAO,CAAC,CAAC,IAAI;SACtE,CAAA;IACH,CAAC;IACD,OAAO,MAAM,CAAA;AACf,CAAC", "sourcesContent": ["import { CancellationToken, configureRequestUrl, newError, safeStringifyJson, UpdateFileInfo, UpdateInfo, WindowsUpdateInfo } from \"builder-util-runtime\"\nimport { OutgoingHttpHeaders, RequestOptions } from \"http\"\nimport { load } from \"js-yaml\"\nimport { URL } from \"url\"\nimport { ElectronHttpExecutor } from \"../electronHttpExecutor\"\nimport { ResolvedUpdateFileInfo } from \"../types\"\nimport { newUrlFromBase } from \"../util\"\n\nexport type ProviderPlatform = \"darwin\" | \"linux\" | \"win32\"\n\nexport interface ProviderRuntimeOptions {\n  isUseMultipleRangeRequest: boolean\n  platform: ProviderPlatform\n\n  executor: ElectronHttpExecutor\n}\n\nexport abstract class Provider<T extends UpdateInfo> {\n  private requestHeaders: OutgoingHttpHeaders | null = null\n  protected readonly executor: ElectronHttpExecutor\n\n  protected constructor(private readonly runtimeOptions: ProviderRuntimeOptions) {\n    this.executor = runtimeOptions.executor\n  }\n\n  get isUseMultipleRangeRequest(): boolean {\n    return this.runtimeOptions.isUseMultipleRangeRequest !== false\n  }\n\n  private getChannelFilePrefix(): string {\n    if (this.runtimeOptions.platform === \"linux\") {\n      const arch = process.env[\"TEST_UPDATER_ARCH\"] || process.arch\n      const archSuffix = arch === \"x64\" ? \"\" : `-${arch}`\n      return \"-linux\" + archSuffix\n    } else {\n      return this.runtimeOptions.platform === \"darwin\" ? \"-mac\" : \"\"\n    }\n  }\n\n  // due to historical reasons for windows we use channel name without platform specifier\n  protected getDefaultChannelName(): string {\n    return this.getCustomChannelName(\"latest\")\n  }\n\n  protected getCustomChannelName(channel: string): string {\n    return `${channel}${this.getChannelFilePrefix()}`\n  }\n\n  get fileExtraDownloadHeaders(): OutgoingHttpHeaders | null {\n    return null\n  }\n\n  setRequestHeaders(value: OutgoingHttpHeaders | null): void {\n    this.requestHeaders = value\n  }\n\n  abstract getLatestVersion(): Promise<T>\n\n  abstract resolveFiles(updateInfo: T): Array<ResolvedUpdateFileInfo>\n\n  /**\n   * Method to perform API request only to resolve update info, but not to download update.\n   */\n  protected httpRequest(url: URL, headers?: OutgoingHttpHeaders | null, cancellationToken?: CancellationToken): Promise<string | null> {\n    return this.executor.request(this.createRequestOptions(url, headers), cancellationToken)\n  }\n\n  protected createRequestOptions(url: URL, headers?: OutgoingHttpHeaders | null): RequestOptions {\n    const result: RequestOptions = {}\n    if (this.requestHeaders == null) {\n      if (headers != null) {\n        result.headers = headers\n      }\n    } else {\n      result.headers = headers == null ? this.requestHeaders : { ...this.requestHeaders, ...headers }\n    }\n\n    configureRequestUrl(url, result)\n    return result\n  }\n}\n\nexport function findFile(files: Array<ResolvedUpdateFileInfo>, extension: string, not?: Array<string>): ResolvedUpdateFileInfo | null | undefined {\n  if (files.length === 0) {\n    throw newError(\"No files provided\", \"ERR_UPDATER_NO_FILES_PROVIDED\")\n  }\n\n  const result = files.find(it => it.url.pathname.toLowerCase().endsWith(`.${extension}`))\n  if (result != null) {\n    return result\n  } else if (not == null) {\n    return files[0]\n  } else {\n    return files.find(fileInfo => !not.some(ext => fileInfo.url.pathname.toLowerCase().endsWith(`.${ext}`)))\n  }\n}\n\nexport function parseUpdateInfo(rawData: string | null, channelFile: string, channelFileUrl: URL): UpdateInfo {\n  if (rawData == null) {\n    throw newError(`Cannot parse update info from ${channelFile} in the latest release artifacts (${channelFileUrl}): rawData: null`, \"ERR_UPDATER_INVALID_UPDATE_INFO\")\n  }\n\n  let result: UpdateInfo\n  try {\n    result = load(rawData) as UpdateInfo\n  } catch (e: any) {\n    throw newError(\n      `Cannot parse update info from ${channelFile} in the latest release artifacts (${channelFileUrl}): ${e.stack || e.message}, rawData: ${rawData}`,\n      \"ERR_UPDATER_INVALID_UPDATE_INFO\"\n    )\n  }\n  return result\n}\n\nexport function getFileList(updateInfo: UpdateInfo): Array<UpdateFileInfo> {\n  const files = updateInfo.files\n  if (files != null && files.length > 0) {\n    return files\n  }\n\n  // noinspection JSDeprecatedSymbols\n  if (updateInfo.path != null) {\n    // noinspection JSDeprecatedSymbols\n    return [\n      {\n        url: updateInfo.path,\n        sha2: (updateInfo as any).sha2,\n        sha512: updateInfo.sha512,\n      } as any,\n    ]\n  } else {\n    throw newError(`No files provided: ${safeStringifyJson(updateInfo)}`, \"ERR_UPDATER_NO_FILES_PROVIDED\")\n  }\n}\n\nexport function resolveFiles(updateInfo: UpdateInfo, baseUrl: URL, pathTransformer: (p: string) => string = (p: string): string => p): Array<ResolvedUpdateFileInfo> {\n  const files = getFileList(updateInfo)\n  const result: Array<ResolvedUpdateFileInfo> = files.map(fileInfo => {\n    if ((fileInfo as any).sha2 == null && fileInfo.sha512 == null) {\n      throw newError(`Update info doesn't contain nor sha256 neither sha512 checksum: ${safeStringifyJson(fileInfo)}`, \"ERR_UPDATER_NO_CHECKSUM\")\n    }\n    return {\n      url: newUrlFromBase(pathTransformer(fileInfo.url), baseUrl),\n      info: fileInfo,\n    }\n  })\n\n  const packages = (updateInfo as WindowsUpdateInfo).packages\n  const packageInfo = packages == null ? null : packages[process.arch] || packages.ia32\n  if (packageInfo != null) {\n    ;(result[0] as any).packageInfo = {\n      ...packageInfo,\n      path: newUrlFromBase(pathTransformer(packageInfo.path), baseUrl).href,\n    }\n  }\n  return result\n}\n"]}