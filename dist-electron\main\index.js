"use strict";
const electron = require("electron");
const path = require("path");
const child_process = require("child_process");
const util = require("util");
const fs = require("fs/promises");
const os = require("os");
const fs$1 = require("fs");
function _interopNamespaceDefault(e) {
  const n = Object.create(null, { [Symbol.toStringTag]: { value: "Module" } });
  if (e) {
    for (const k in e) {
      if (k !== "default") {
        const d = Object.getOwnPropertyDescriptor(e, k);
        Object.defineProperty(n, k, d.get ? d : {
          enumerable: true,
          get: () => e[k]
        });
      }
    }
  }
  n.default = e;
  return Object.freeze(n);
}
const path__namespace = /* @__PURE__ */ _interopNamespaceDefault(path);
const fs__namespace = /* @__PURE__ */ _interopNamespaceDefault(fs);
const os__namespace = /* @__PURE__ */ _interopNamespaceDefault(os);
const fs__namespace$1 = /* @__PURE__ */ _interopNamespaceDefault(fs$1);
const execAsync = util.promisify(child_process.exec);
class WindowsSystemAPI {
  async cleanSystemCache() {
    const result = {
      success: true,
      freedSpace: 0,
      errors: [],
      details: []
    };
    try {
      const tempPaths = [
        path__namespace.join(os__namespace.tmpdir()),
        path__namespace.join(process.env.LOCALAPPDATA || "", "Temp"),
        path__namespace.join(process.env.WINDIR || "C:\\Windows", "Temp")
      ];
      for (const tempPath of tempPaths) {
        try {
          const sizeBefore = await this.getDirectorySize(tempPath);
          await this.cleanDirectory(tempPath, true);
          const sizeAfter = await this.getDirectorySize(tempPath);
          const freed = sizeBefore - sizeAfter;
          result.freedSpace += freed;
          result.details.push(`Cleaned ${tempPath}: ${this.formatBytes(freed)} freed`);
        } catch (error) {
          result.errors.push(`Failed to clean ${tempPath}: ${error}`);
        }
      }
      await this.cleanBrowserCaches(result);
      result.details.push("System cache cleanup completed");
    } catch (error) {
      result.success = false;
      result.errors.push(`Cache cleaning failed: ${error}`);
    }
    return result;
  }
  async cleanTempFiles() {
    const result = {
      success: true,
      freedSpace: 0,
      errors: [],
      details: []
    };
    try {
      const tempDirs = [
        os__namespace.tmpdir(),
        path__namespace.join(process.env.LOCALAPPDATA || "", "Temp"),
        path__namespace.join(process.env.APPDATA || "", "Local", "Temp")
      ];
      for (const dir of tempDirs) {
        try {
          const sizeBefore = await this.getDirectorySize(dir);
          await this.cleanDirectory(dir, true);
          const sizeAfter = await this.getDirectorySize(dir);
          const freed = sizeBefore - sizeAfter;
          result.freedSpace += freed;
          result.details.push(`Cleaned temp directory ${dir}: ${this.formatBytes(freed)} freed`);
        } catch (error) {
          result.errors.push(`Failed to clean ${dir}: ${error}`);
        }
      }
    } catch (error) {
      result.success = false;
      result.errors.push(`Temp file cleaning failed: ${error}`);
    }
    return result;
  }
  async getInstalledGames() {
    const games = [];
    try {
      console.log("Starting game detection...");
      console.log("Scanning Steam games...");
      const steamGames = await this.getSteamGames();
      console.log(`Found ${steamGames.length} Steam games`);
      games.push(...steamGames);
      console.log("Scanning Epic games...");
      const epicGames = await this.getEpicGames();
      console.log(`Found ${epicGames.length} Epic games`);
      games.push(...epicGames);
      console.log("Scanning Windows Store games...");
      const storeGames = await this.getWindowsStoreGames();
      console.log(`Found ${storeGames.length} Windows Store games`);
      games.push(...storeGames);
      console.log("Scanning common directories...");
      const commonGames = await this.getCommonDirectoryGames();
      console.log(`Found ${commonGames.length} games in common directories`);
      games.push(...commonGames);
      if (games.length === 0) {
        console.log("No games detected, adding mock data for testing...");
        games.push(...this.getMockGames());
      }
      console.log(`Total games found: ${games.length}`);
    } catch (error) {
      console.error("Failed to get installed games:", error);
      games.push(...this.getMockGames());
    }
    return games;
  }
  getMockGames() {
    return [
      {
        name: "Steam (Detected)",
        path: "C:\\Program Files (x86)\\Steam\\steam.exe",
        size: 1024 * 1024 * 100,
        // 100 MB
        lastPlayed: new Date(Date.now() - 2 * 24 * 60 * 60 * 1e3),
        platform: "Steam"
      },
      {
        name: "Google Chrome (Browser)",
        path: "C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe",
        size: 1024 * 1024 * 200,
        // 200 MB
        lastPlayed: new Date(Date.now() - 1 * 60 * 60 * 1e3),
        platform: "Other"
      },
      {
        name: "Visual Studio Code",
        path: "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe",
        size: 1024 * 1024 * 300,
        // 300 MB
        lastPlayed: new Date(Date.now() - 3 * 60 * 60 * 1e3),
        platform: "Other"
      }
    ];
  }
  async launchGame(gamePath) {
    try {
      child_process.spawn(gamePath, [], { detached: true, stdio: "ignore" });
      return true;
    } catch (error) {
      console.error("Failed to launch game:", error);
      return false;
    }
  }
  async getSystemHealth() {
    const health = {
      diskHealth: [],
      memoryUsage: { total: 0, used: 0, available: 0 },
      cpuUsage: 0,
      systemErrors: []
    };
    try {
      health.diskHealth = await this.getDiskHealth();
      health.memoryUsage = await this.getMemoryUsage();
      health.cpuUsage = await this.getCPUUsage();
      health.systemErrors = await this.getSystemErrors();
    } catch (error) {
      console.error("Failed to get system health:", error);
    }
    return health;
  }
  async optimizeWindows(options) {
    const result = {
      success: true,
      freedSpace: 0,
      errors: [],
      details: []
    };
    try {
      if (options.disableStartupPrograms) {
        await this.optimizeStartupPrograms(result);
      }
      if (options.disableUnnecessaryServices) {
        await this.optimizeServices(result);
      }
      if (options.optimizeVisualEffects) {
        await this.optimizeVisualEffects(result);
      }
      if (options.cleanRegistry) {
        await this.cleanRegistry(result);
      }
    } catch (error) {
      result.success = false;
      result.errors.push(`Windows optimization failed: ${error}`);
    }
    return result;
  }
  // Helper methods
  async getDirectorySize(dirPath) {
    try {
      const stats = await fs__namespace.stat(dirPath);
      if (!stats.isDirectory())
        return stats.size;
      let totalSize = 0;
      const items = await fs__namespace.readdir(dirPath);
      for (const item of items) {
        const itemPath = path__namespace.join(dirPath, item);
        try {
          const itemStats = await fs__namespace.stat(itemPath);
          if (itemStats.isDirectory()) {
            totalSize += await this.getDirectorySize(itemPath);
          } else {
            totalSize += itemStats.size;
          }
        } catch (error) {
        }
      }
      return totalSize;
    } catch (error) {
      return 0;
    }
  }
  async cleanDirectory(dirPath, keepStructure = false) {
    try {
      const items = await fs__namespace.readdir(dirPath);
      for (const item of items) {
        const itemPath = path__namespace.join(dirPath, item);
        try {
          const stats = await fs__namespace.stat(itemPath);
          if (stats.isDirectory()) {
            if (keepStructure) {
              await this.cleanDirectory(itemPath, true);
            } else {
              await fs__namespace.rmdir(itemPath, { recursive: true });
            }
          } else {
            await fs__namespace.unlink(itemPath);
          }
        } catch (error) {
        }
      }
    } catch (error) {
    }
  }
  formatBytes(bytes) {
    if (bytes === 0)
      return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB", "TB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  }
  // Additional helper methods would be implemented here...
  async cleanBrowserCaches(result) {
    const browserPaths = [
      // Chrome
      path__namespace.join(process.env.LOCALAPPDATA || "", "Google", "Chrome", "User Data", "Default", "Cache"),
      // Firefox
      path__namespace.join(process.env.APPDATA || "", "Mozilla", "Firefox", "Profiles"),
      // Edge
      path__namespace.join(process.env.LOCALAPPDATA || "", "Microsoft", "Edge", "User Data", "Default", "Cache"),
      // Opera
      path__namespace.join(process.env.APPDATA || "", "Opera Software", "Opera Stable", "Cache")
    ];
    for (const browserPath of browserPaths) {
      try {
        if (await this.pathExists(browserPath)) {
          const sizeBefore = await this.getDirectorySize(browserPath);
          await this.cleanDirectory(browserPath, true);
          const sizeAfter = await this.getDirectorySize(browserPath);
          const freed = sizeBefore - sizeAfter;
          result.freedSpace += freed;
          result.details.push(`Cleaned browser cache: ${this.formatBytes(freed)} freed`);
        }
      } catch (error) {
        result.errors.push(`Failed to clean browser cache: ${error}`);
      }
    }
  }
  async getSteamGames() {
    const games = [];
    try {
      const steamPath = await this.getSteamPathFromRegistry();
      if (steamPath) {
        await this.scanSteamDirectory(steamPath, games);
      } else {
        const steamPaths = [
          path__namespace.join("C:", "Program Files (x86)", "Steam"),
          path__namespace.join("C:", "Program Files", "Steam"),
          path__namespace.join(process.env.PROGRAMFILES || "", "Steam"),
          path__namespace.join(process.env["PROGRAMFILES(X86)"] || "", "Steam")
        ];
        for (const steamPath2 of steamPaths) {
          if (await this.pathExists(steamPath2)) {
            await this.scanSteamDirectory(steamPath2, games);
            break;
          }
        }
      }
      await this.scanSteamLibraryFolders(games);
    } catch (error) {
      console.error("Error scanning Steam games:", error);
    }
    return games;
  }
  async getSteamPathFromRegistry() {
    try {
      const { stdout } = await execAsync('reg query "HKEY_LOCAL_MACHINE\\SOFTWARE\\WOW6432Node\\Valve\\Steam" /v InstallPath');
      const match = stdout.match(/InstallPath\s+REG_SZ\s+(.+)/);
      return match ? match[1].trim() : null;
    } catch (error) {
      try {
        const { stdout } = await execAsync('reg query "HKEY_LOCAL_MACHINE\\SOFTWARE\\Valve\\Steam" /v InstallPath');
        const match = stdout.match(/InstallPath\s+REG_SZ\s+(.+)/);
        return match ? match[1].trim() : null;
      } catch (error2) {
        return null;
      }
    }
  }
  async scanSteamDirectory(steamPath, games) {
    const steamAppsPath = path__namespace.join(steamPath, "steamapps", "common");
    if (await this.pathExists(steamAppsPath)) {
      const gameDirectories = await fs__namespace.readdir(steamAppsPath);
      for (const gameDir of gameDirectories) {
        const gamePath = path__namespace.join(steamAppsPath, gameDir);
        const gameInfo = await this.analyzeGameDirectory(gamePath, "Steam");
        if (gameInfo) {
          games.push(gameInfo);
        }
      }
    }
  }
  async scanSteamLibraryFolders(games) {
    try {
      const steamPath = await this.getSteamPathFromRegistry();
      if (!steamPath)
        return;
      const configPath = path__namespace.join(steamPath, "steamapps", "libraryfolders.vdf");
      if (await this.pathExists(configPath)) {
        const configContent = await fs__namespace.readFile(configPath, "utf-8");
        const pathMatches = configContent.match(/"path"\s+"([^"]+)"/g);
        if (pathMatches) {
          for (const match of pathMatches) {
            const libraryPath = match.match(/"path"\s+"([^"]+)"/)?.[1];
            if (libraryPath) {
              const commonPath = path__namespace.join(libraryPath, "steamapps", "common");
              if (await this.pathExists(commonPath)) {
                await this.scanSteamDirectory(libraryPath, games);
              }
            }
          }
        }
      }
    } catch (error) {
      console.error("Error scanning Steam library folders:", error);
    }
  }
  async getEpicGames() {
    const games = [];
    try {
      const epicManifestPaths = [
        path__namespace.join(process.env.PROGRAMDATA || "C:\\ProgramData", "Epic", "EpicGamesLauncher", "Data", "Manifests"),
        path__namespace.join(process.env.LOCALAPPDATA || "", "EpicGamesLauncher", "Saved", "Config", "Windows"),
        path__namespace.join(process.env.APPDATA || "", "Epic", "EpicGamesLauncher", "Saved", "Config", "Windows")
      ];
      for (const epicManifestPath of epicManifestPaths) {
        if (await this.pathExists(epicManifestPath)) {
          try {
            const manifestFiles = await fs__namespace.readdir(epicManifestPath);
            for (const manifestFile of manifestFiles) {
              if (manifestFile.endsWith(".item")) {
                try {
                  const manifestPath = path__namespace.join(epicManifestPath, manifestFile);
                  const manifestContent = await fs__namespace.readFile(manifestPath, "utf-8");
                  const manifest = JSON.parse(manifestContent);
                  if (manifest.InstallLocation && manifest.DisplayName) {
                    const gameInfo = await this.analyzeGameDirectory(manifest.InstallLocation, "Epic");
                    if (gameInfo) {
                      gameInfo.name = manifest.DisplayName;
                      games.push(gameInfo);
                    }
                  }
                } catch (error) {
                }
              }
            }
          } catch (error) {
          }
        }
      }
      const epicGamesPaths = [
        path__namespace.join("C:", "Program Files", "Epic Games"),
        path__namespace.join("C:", "Program Files (x86)", "Epic Games"),
        path__namespace.join(process.env.PROGRAMFILES || "", "Epic Games"),
        path__namespace.join(process.env["PROGRAMFILES(X86)"] || "", "Epic Games")
      ];
      for (const epicPath of epicGamesPaths) {
        if (await this.pathExists(epicPath)) {
          try {
            const gameDirectories = await fs__namespace.readdir(epicPath);
            for (const gameDir of gameDirectories) {
              if (gameDir === "Launcher")
                continue;
              const gamePath = path__namespace.join(epicPath, gameDir);
              const gameInfo = await this.analyzeGameDirectory(gamePath, "Epic");
              if (gameInfo) {
                games.push(gameInfo);
              }
            }
          } catch (error) {
          }
        }
      }
    } catch (error) {
      console.error("Error scanning Epic Games:", error);
    }
    return games;
  }
  async getWindowsStoreGames() {
    const games = [];
    try {
      const windowsAppsPath = path__namespace.join("C:", "Program Files", "WindowsApps");
      if (await this.pathExists(windowsAppsPath)) {
        const { stdout } = await execAsync(`powershell "Get-AppxPackage | Where-Object {$_.PackageFullName -like '*Game*' -or $_.PackageFullName -like '*game*'} | Select-Object Name, InstallLocation"`);
        const lines = stdout.split("\n");
        let currentGame = {};
        for (const line of lines) {
          if (line.includes("Name")) {
            currentGame.name = line.split(":")[1]?.trim();
          } else if (line.includes("InstallLocation")) {
            currentGame.path = line.split(":")[1]?.trim();
            if (currentGame.name && currentGame.path) {
              const gameInfo = await this.analyzeGameDirectory(currentGame.path, "Windows Store");
              if (gameInfo) {
                gameInfo.name = currentGame.name;
                games.push(gameInfo);
              }
            }
            currentGame = {};
          }
        }
      }
    } catch (error) {
      console.error("Error scanning Windows Store games:", error);
    }
    return games;
  }
  async getCommonDirectoryGames() {
    const games = [];
    const commonGamePaths = [
      "C:\\Games",
      "C:\\Program Files\\Games",
      "C:\\Program Files (x86)\\Games",
      path__namespace.join(process.env.USERPROFILE || "", "Games"),
      "D:\\Games",
      "E:\\Games",
      "F:\\Games",
      // Riot Games
      "C:\\Riot Games",
      path__namespace.join(process.env.PROGRAMFILES || "", "Riot Games"),
      // Blizzard
      "C:\\Program Files (x86)\\Battle.net",
      path__namespace.join(process.env.PROGRAMFILES || "", "Battle.net"),
      // Origin
      "C:\\Program Files (x86)\\Origin Games",
      path__namespace.join(process.env.PROGRAMFILES || "", "Origin Games"),
      // Ubisoft
      "C:\\Program Files (x86)\\Ubisoft",
      path__namespace.join(process.env.PROGRAMFILES || "", "Ubisoft"),
      // GOG
      "C:\\GOG Games",
      "C:\\Program Files (x86)\\GOG Galaxy\\Games",
      // Rockstar
      "C:\\Program Files\\Rockstar Games",
      "C:\\Program Files (x86)\\Rockstar Games"
    ];
    for (const gamePath of commonGamePaths) {
      try {
        if (await this.pathExists(gamePath)) {
          await this.scanGameDirectory(gamePath, games, "Other");
        }
      } catch (error) {
      }
    }
    await this.scanInstalledPrograms(games);
    return games;
  }
  async scanGameDirectory(basePath, games, platform) {
    try {
      const gameDirectories = await fs__namespace.readdir(basePath);
      for (const gameDir of gameDirectories) {
        const fullGamePath = path__namespace.join(basePath, gameDir);
        const stats = await fs__namespace.stat(fullGamePath);
        if (stats.isDirectory()) {
          const gameInfo = await this.analyzeGameDirectory(fullGamePath, platform);
          if (gameInfo) {
            games.push(gameInfo);
          }
        }
      }
    } catch (error) {
    }
  }
  async scanInstalledPrograms(games) {
    try {
      const registryPaths = [
        "HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall",
        "HKEY_LOCAL_MACHINE\\SOFTWARE\\WOW6432Node\\Microsoft\\Windows\\CurrentVersion\\Uninstall",
        "HKEY_CURRENT_USER\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall"
      ];
      for (const regPath of registryPaths) {
        try {
          const { stdout } = await execAsync(`reg query "${regPath}" /s`);
          const lines = stdout.split("\n");
          let currentApp = {};
          for (const line of lines) {
            if (line.includes("DisplayName")) {
              const match = line.match(/DisplayName\s+REG_SZ\s+(.+)/);
              if (match) {
                currentApp.name = match[1].trim();
              }
            } else if (line.includes("InstallLocation")) {
              const match = line.match(/InstallLocation\s+REG_SZ\s+(.+)/);
              if (match) {
                currentApp.path = match[1].trim();
              }
            } else if (line.includes("Publisher")) {
              const match = line.match(/Publisher\s+REG_SZ\s+(.+)/);
              if (match) {
                currentApp.publisher = match[1].trim();
              }
            }
            if (currentApp.name && currentApp.path && this.looksLikeGame(currentApp)) {
              const gameInfo = await this.analyzeGameDirectory(currentApp.path, this.detectPlatformFromPublisher(currentApp.publisher));
              if (gameInfo) {
                gameInfo.name = currentApp.name;
                games.push(gameInfo);
              }
              currentApp = {};
            }
          }
        } catch (error) {
        }
      }
    } catch (error) {
      console.error("Error scanning installed programs:", error);
    }
  }
  looksLikeGame(app) {
    const gameKeywords = [
      "game",
      "games",
      "gaming",
      "steam",
      "epic",
      "origin",
      "uplay",
      "battle.net",
      "blizzard",
      "riot",
      "valve",
      "rockstar",
      "ubisoft",
      "ea",
      "activision",
      "bethesda",
      "cd projekt",
      "square enix",
      "capcom",
      "konami",
      "sega",
      "nintendo",
      "sony",
      "microsoft",
      "xbox",
      "playstation"
    ];
    const name = (app.name || "").toLowerCase();
    const publisher = (app.publisher || "").toLowerCase();
    return gameKeywords.some(
      (keyword) => name.includes(keyword) || publisher.includes(keyword)
    ) || this.getGameDatabase().some(
      (game) => game.folderNames.some((folder) => name.includes(folder.toLowerCase()))
    );
  }
  detectPlatformFromPublisher(publisher) {
    if (!publisher)
      return "Other";
    const pub = publisher.toLowerCase();
    if (pub.includes("valve") || pub.includes("steam"))
      return "Steam";
    if (pub.includes("epic") || pub.includes("epic games"))
      return "Epic";
    if (pub.includes("microsoft") || pub.includes("xbox"))
      return "Windows Store";
    if (pub.includes("blizzard") || pub.includes("battle.net"))
      return "Battle.net";
    if (pub.includes("origin") || pub.includes("ea"))
      return "Origin";
    if (pub.includes("ubisoft") || pub.includes("uplay"))
      return "Uplay";
    if (pub.includes("riot"))
      return "Riot Games";
    if (pub.includes("rockstar"))
      return "Rockstar";
    if (pub.includes("gog"))
      return "GOG";
    return "Other";
  }
  async getDiskHealth() {
    try {
      const { stdout } = await execAsync('powershell "Get-WmiObject -Class Win32_LogicalDisk | Select-Object DeviceID, Size, FreeSpace | ConvertTo-Json"');
      const disks = JSON.parse(stdout);
      const diskArray = Array.isArray(disks) ? disks : [disks];
      return diskArray.map((disk) => {
        const totalSpace = parseInt(disk.Size) || 0;
        const freeSpace = parseInt(disk.FreeSpace) || 0;
        const usedSpace = totalSpace - freeSpace;
        const usagePercent = totalSpace > 0 ? usedSpace / totalSpace * 100 : 0;
        return {
          drive: disk.DeviceID,
          totalSpace,
          freeSpace,
          health: usagePercent > 90 ? "critical" : usagePercent > 75 ? "warning" : "good"
        };
      });
    } catch (error) {
      console.error("Failed to get disk health:", error);
      return [{
        drive: "C:",
        totalSpace: 1e12,
        // 1TB
        freeSpace: 5e11,
        // 500GB
        health: "good"
      }];
    }
  }
  async getMemoryUsage() {
    try {
      const totalMem = os__namespace.totalmem();
      const freeMem = os__namespace.freemem();
      const usedMem = totalMem - freeMem;
      return {
        total: totalMem,
        used: usedMem,
        available: freeMem
      };
    } catch (error) {
      console.error("Failed to get memory usage:", error);
      return {
        total: 16e9,
        // 16GB
        used: 8e9,
        // 8GB
        available: 8e9
        // 8GB
      };
    }
  }
  async getCPUUsage() {
    try {
      const { stdout } = await execAsync('powershell "Get-WmiObject -Class Win32_Processor | Measure-Object -Property LoadPercentage -Average | Select-Object Average | ConvertTo-Json"');
      const result = JSON.parse(stdout);
      if (result && result.Average !== null) {
        return Math.round(result.Average);
      }
      const cpus = os__namespace.cpus();
      let totalIdle = 0;
      let totalTick = 0;
      cpus.forEach((cpu) => {
        for (const type in cpu.times) {
          totalTick += cpu.times[type];
        }
        totalIdle += cpu.times.idle;
      });
      const idle = totalIdle / cpus.length;
      const total = totalTick / cpus.length;
      const usage = 100 - ~~(100 * idle / total);
      return Math.max(0, Math.min(100, usage));
    } catch (error) {
      console.error("Failed to get CPU usage:", error);
      return Math.floor(Math.random() * 30) + 10;
    }
  }
  async getSystemErrors() {
    try {
      const { stdout } = await execAsync('wevtutil qe System /c:10 /rd:true /f:text /q:"*[System[(Level=1 or Level=2)]]"');
      const errors = stdout.split("\n").filter((line) => line.trim()).slice(0, 5);
      return errors.length > 0 ? errors : [];
    } catch (error) {
      console.error("Failed to get system errors:", error);
      return [];
    }
  }
  async optimizeStartupPrograms(result) {
  }
  async optimizeServices(result) {
  }
  async optimizeVisualEffects(result) {
  }
  async cleanRegistry(result) {
  }
  // Game analysis methods
  async analyzeGameDirectory(gamePath, platform) {
    try {
      const stats = await fs__namespace.stat(gamePath);
      if (!stats.isDirectory())
        return null;
      const files = await fs__namespace.readdir(gamePath);
      const executableFiles = files.filter(
        (file) => file.endsWith(".exe") && !file.toLowerCase().includes("uninstall") && !file.toLowerCase().includes("setup") && !file.toLowerCase().includes("installer")
      );
      if (executableFiles.length === 0)
        return null;
      let mainExecutable = executableFiles[0];
      const gameDatabase = this.getGameDatabase();
      const gameName = path__namespace.basename(gamePath);
      const knownGame = gameDatabase.find(
        (game) => game.folderNames.some(
          (folder) => gameName.toLowerCase().includes(folder.toLowerCase())
        )
      );
      if (knownGame) {
        const knownExe = executableFiles.find(
          (exe) => knownGame.executableNames.some(
            (name) => exe.toLowerCase().includes(name.toLowerCase())
          )
        );
        if (knownExe) {
          mainExecutable = knownExe;
        }
      }
      const executablePath = path__namespace.join(gamePath, mainExecutable);
      const directorySize = await this.getDirectorySize(gamePath);
      const executableStats = await fs__namespace.stat(executablePath);
      return {
        name: knownGame?.displayName || this.formatGameName(gameName),
        path: executablePath,
        size: directorySize,
        lastPlayed: executableStats.mtime,
        platform,
        icon: await this.extractGameIcon(executablePath)
      };
    } catch (error) {
      return null;
    }
  }
  formatGameName(folderName) {
    return folderName.replace(/[_-]/g, " ").replace(/\b\w/g, (l) => l.toUpperCase()).trim();
  }
  async extractGameIcon(executablePath) {
    try {
      return void 0;
    } catch (error) {
      return void 0;
    }
  }
  async pathExists(path2) {
    try {
      await fs__namespace.access(path2);
      return true;
    } catch {
      return false;
    }
  }
  getGameDatabase() {
    return [
      {
        displayName: "Cyberpunk 2077",
        folderNames: ["cyberpunk", "cyberpunk2077", "cyberpunk 2077"],
        executableNames: ["cyberpunk2077.exe", "cyberpunk.exe"]
      },
      {
        displayName: "The Witcher 3: Wild Hunt",
        folderNames: ["witcher3", "the witcher 3", "witcher 3"],
        executableNames: ["witcher3.exe", "tw3.exe"]
      },
      {
        displayName: "Grand Theft Auto V",
        folderNames: ["gtav", "gta5", "grand theft auto v", "gta v"],
        executableNames: ["gtav.exe", "gta5.exe", "playgtav.exe"]
      },
      {
        displayName: "Counter-Strike 2",
        folderNames: ["counter-strike", "cs2", "counterstrike"],
        executableNames: ["cs2.exe", "csgo.exe"]
      },
      {
        displayName: "Valorant",
        folderNames: ["valorant", "riot games"],
        executableNames: ["valorant.exe", "valorant-win64-shipping.exe"]
      },
      {
        displayName: "League of Legends",
        folderNames: ["league of legends", "lol", "riot games"],
        executableNames: ["league of legends.exe", "lol.exe"]
      },
      {
        displayName: "Fortnite",
        folderNames: ["fortnite", "fortnitegame"],
        executableNames: ["fortniteclient-win64-shipping.exe", "fortnite.exe"]
      },
      {
        displayName: "Minecraft",
        folderNames: ["minecraft", ".minecraft"],
        executableNames: ["minecraft.exe", "minecraftlauncher.exe"]
      },
      {
        displayName: "World of Warcraft",
        folderNames: ["world of warcraft", "wow", "_retail_"],
        executableNames: ["wow.exe", "worldofwarcraft.exe"]
      },
      {
        displayName: "Call of Duty: Modern Warfare",
        folderNames: ["call of duty", "modern warfare", "cod"],
        executableNames: ["modernwarfare.exe", "cod.exe"]
      },
      {
        displayName: "Apex Legends",
        folderNames: ["apex legends", "apex"],
        executableNames: ["r5apex.exe", "apex.exe"]
      },
      {
        displayName: "Red Dead Redemption 2",
        folderNames: ["red dead redemption", "rdr2"],
        executableNames: ["rdr2.exe", "reddeadredemption2.exe"]
      },
      {
        displayName: "Assassin's Creed Valhalla",
        folderNames: ["assassins creed", "valhalla", "ac valhalla"],
        executableNames: ["acvalhalla.exe", "assassinscreedvalhalla.exe"]
      },
      {
        displayName: "FIFA 24",
        folderNames: ["fifa", "fifa24", "ea sports fc"],
        executableNames: ["fifa24.exe", "fc24.exe"]
      },
      {
        displayName: "Rocket League",
        folderNames: ["rocket league", "rocketleague"],
        executableNames: ["rocketleague.exe", "rl.exe"]
      },
      {
        displayName: "Among Us",
        folderNames: ["among us", "amongus"],
        executableNames: ["among us.exe", "amongus.exe"]
      },
      {
        displayName: "Fall Guys",
        folderNames: ["fall guys", "fallguys"],
        executableNames: ["fallguys_client.exe", "fallguys.exe"]
      },
      {
        displayName: "Genshin Impact",
        folderNames: ["genshin impact", "genshinimpact"],
        executableNames: ["genshinimpact.exe", "yuanshen.exe"]
      },
      {
        displayName: "Overwatch 2",
        folderNames: ["overwatch", "overwatch 2"],
        executableNames: ["overwatch.exe", "overwatch2.exe"]
      },
      {
        displayName: "Destiny 2",
        folderNames: ["destiny 2", "destiny2"],
        executableNames: ["destiny2.exe", "destiny2launcher.exe"]
      },
      {
        displayName: "Steam",
        folderNames: ["steam"],
        executableNames: ["steam.exe"]
      },
      {
        displayName: "Epic Games Launcher",
        folderNames: ["epic games", "epicgameslauncher"],
        executableNames: ["epicgameslauncher.exe", "epic.exe"]
      },
      {
        displayName: "Discord",
        folderNames: ["discord"],
        executableNames: ["discord.exe"]
      },
      {
        displayName: "Spotify",
        folderNames: ["spotify"],
        executableNames: ["spotify.exe"]
      },
      {
        displayName: "OBS Studio",
        folderNames: ["obs", "obs studio"],
        executableNames: ["obs64.exe", "obs32.exe"]
      }
    ];
  }
}
function createWindow() {
  const mainWindow = new electron.BrowserWindow({
    width: 1200,
    height: 800,
    minWidth: 1e3,
    minHeight: 700,
    show: false,
    autoHideMenuBar: true,
    title: "TurboBoost Pro",
    // ...(process.platform === 'linux' ? { icon } : {}),
    webPreferences: {
      preload: path.join(__dirname, "../preload/index.js"),
      sandbox: false,
      contextIsolation: true,
      enableRemoteModule: false,
      nodeIntegration: false
    },
    titleBarStyle: "hidden",
    titleBarOverlay: {
      color: "#1e1e1e",
      symbolColor: "#ffffff"
    }
  });
  mainWindow.on("ready-to-show", () => {
    mainWindow.show();
  });
  mainWindow.webContents.setWindowOpenHandler((details) => {
    electron.shell.openExternal(details.url);
    return { action: "deny" };
  });
  if (process.env["ELECTRON_RENDERER_URL"]) {
    mainWindow.loadURL(process.env["ELECTRON_RENDERER_URL"]);
  } else {
    mainWindow.loadFile(path.join(__dirname, "../renderer/index.html"));
  }
}
electron.app.whenReady().then(() => {
  electron.app.setAppUserModelId("com.sulindvaas.turboboost");
  createWindow();
  electron.app.on("activate", function() {
    if (electron.BrowserWindow.getAllWindows().length === 0)
      createWindow();
  });
});
electron.app.on("window-all-closed", () => {
  if (process.platform !== "darwin") {
    electron.app.quit();
  }
});
const systemAPI = new WindowsSystemAPI();
electron.ipcMain.handle("system:cleanCache", async () => {
  try {
    return await systemAPI.cleanSystemCache();
  } catch (error) {
    console.error("Cache cleaning failed:", error);
    throw error;
  }
});
electron.ipcMain.handle("system:cleanTempFiles", async () => {
  try {
    return await systemAPI.cleanTempFiles();
  } catch (error) {
    console.error("Temp files cleaning failed:", error);
    throw error;
  }
});
electron.ipcMain.handle("system:getInstalledGames", async () => {
  try {
    return await systemAPI.getInstalledGames();
  } catch (error) {
    console.error("Failed to get installed games:", error);
    throw error;
  }
});
electron.ipcMain.handle("system:launchGame", async (_, gamePath) => {
  try {
    return await systemAPI.launchGame(gamePath);
  } catch (error) {
    console.error("Failed to launch game:", error);
    throw error;
  }
});
electron.ipcMain.handle("system:getSystemHealth", async () => {
  try {
    return await systemAPI.getSystemHealth();
  } catch (error) {
    console.error("Failed to get system health:", error);
    throw error;
  }
});
electron.ipcMain.handle("system:optimizeWindows", async (_, options) => {
  try {
    return await systemAPI.optimizeWindows(options);
  } catch (error) {
    console.error("Windows optimization failed:", error);
    throw error;
  }
});
electron.ipcMain.handle("dialog:showMessageBox", async (_, options) => {
  const result = await electron.dialog.showMessageBox(options);
  return result;
});
const settingsPath = path__namespace.join(os__namespace.homedir(), ".turboboost-pro", "settings.json");
electron.ipcMain.handle("settings:get", async () => {
  try {
    const settingsDir = path__namespace.dirname(settingsPath);
    if (!fs__namespace$1.existsSync(settingsDir)) {
      fs__namespace$1.mkdirSync(settingsDir, { recursive: true });
    }
    if (fs__namespace$1.existsSync(settingsPath)) {
      const data = fs__namespace$1.readFileSync(settingsPath, "utf-8");
      return JSON.parse(data);
    }
    return {
      theme: "dark",
      minimizeToTray: true,
      startWithWindows: false,
      autoStartCleanup: false,
      notifications: true,
      autoUpdates: true,
      telemetry: false,
      language: "en",
      cpuUsageLimit: 80,
      memoryUsageLimit: 70,
      cleanupInterval: 7,
      scanDepth: 3,
      animationSpeed: 50,
      notificationVolume: 75
    };
  } catch (error) {
    console.error("Failed to load settings:", error);
    return null;
  }
});
electron.ipcMain.handle("settings:save", async (_, settings) => {
  try {
    const settingsDir = path__namespace.dirname(settingsPath);
    if (!fs__namespace$1.existsSync(settingsDir)) {
      fs__namespace$1.mkdirSync(settingsDir, { recursive: true });
    }
    fs__namespace$1.writeFileSync(settingsPath, JSON.stringify(settings, null, 2));
    return true;
  } catch (error) {
    console.error("Failed to save settings:", error);
    throw error;
  }
});
electron.ipcMain.handle("app:minimize", async () => {
  const window = electron.BrowserWindow.getFocusedWindow();
  if (window) {
    window.minimize();
  }
});
electron.ipcMain.handle("app:close", async () => {
  const window = electron.BrowserWindow.getFocusedWindow();
  if (window) {
    window.close();
  }
});
