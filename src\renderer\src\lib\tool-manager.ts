interface ToolExecution {
  toolId: string
  lastExecuted: number
  isExecuting: boolean
  cooldownPeriod: number // in milliseconds
}

class ToolManager {
  private executions: Map<string, ToolExecution> = new Map()
  private listeners: Map<string, ((state: ToolExecution) => void)[]> = new Map()

  // Default cooldown periods for different tools (in milliseconds)
  private defaultCooldowns = {
    'cache-cleaner': 30000, // 30 seconds
    'temp-cleaner': 30000, // 30 seconds
    'game-scanner': 60000, // 1 minute
    'system-optimizer': 120000, // 2 minutes
    'registry-cleaner': 180000, // 3 minutes
    'startup-optimizer': 120000, // 2 minutes
    'service-optimizer': 180000, // 3 minutes
    'visual-effects': 60000, // 1 minute
    'system-health': 30000, // 30 seconds
  }

  canExecute(toolId: string): boolean {
    const execution = this.executions.get(toolId)
    
    if (!execution) {
      return true
    }

    if (execution.isExecuting) {
      return false
    }

    const now = Date.now()
    const timeSinceLastExecution = now - execution.lastExecuted
    
    return timeSinceLastExecution >= execution.cooldownPeriod
  }

  startExecution(toolId: string, customCooldown?: number): boolean {
    if (!this.canExecute(toolId)) {
      return false
    }

    const cooldownPeriod = customCooldown || this.defaultCooldowns[toolId] || 60000
    
    const execution: ToolExecution = {
      toolId,
      lastExecuted: Date.now(),
      isExecuting: true,
      cooldownPeriod
    }

    this.executions.set(toolId, execution)
    this.notifyListeners(toolId, execution)
    
    return true
  }

  finishExecution(toolId: string): void {
    const execution = this.executions.get(toolId)
    
    if (execution) {
      execution.isExecuting = false
      execution.lastExecuted = Date.now()
      this.executions.set(toolId, execution)
      this.notifyListeners(toolId, execution)
    }
  }

  getExecutionState(toolId: string): ToolExecution | null {
    return this.executions.get(toolId) || null
  }

  getRemainingCooldown(toolId: string): number {
    const execution = this.executions.get(toolId)
    
    if (!execution || execution.isExecuting) {
      return 0
    }

    const now = Date.now()
    const timeSinceLastExecution = now - execution.lastExecuted
    const remaining = execution.cooldownPeriod - timeSinceLastExecution
    
    return Math.max(0, remaining)
  }

  isExecuting(toolId: string): boolean {
    const execution = this.executions.get(toolId)
    return execution?.isExecuting || false
  }

  subscribe(toolId: string, callback: (state: ToolExecution) => void): () => void {
    if (!this.listeners.has(toolId)) {
      this.listeners.set(toolId, [])
    }
    
    this.listeners.get(toolId)!.push(callback)
    
    // Return unsubscribe function
    return () => {
      const callbacks = this.listeners.get(toolId)
      if (callbacks) {
        const index = callbacks.indexOf(callback)
        if (index > -1) {
          callbacks.splice(index, 1)
        }
      }
    }
  }

  private notifyListeners(toolId: string, execution: ToolExecution): void {
    const callbacks = this.listeners.get(toolId)
    if (callbacks) {
      callbacks.forEach(callback => callback(execution))
    }
  }

  // Utility method to format remaining time
  formatRemainingTime(toolId: string): string {
    const remaining = this.getRemainingCooldown(toolId)
    
    if (remaining === 0) {
      return ''
    }

    const seconds = Math.ceil(remaining / 1000)
    
    if (seconds < 60) {
      return `${seconds}s`
    }
    
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    
    if (remainingSeconds === 0) {
      return `${minutes}m`
    }
    
    return `${minutes}m ${remainingSeconds}s`
  }

  // Reset all executions (useful for testing or manual reset)
  reset(): void {
    this.executions.clear()
    this.listeners.clear()
  }

  // Get all tool states
  getAllStates(): Record<string, ToolExecution> {
    const states: Record<string, ToolExecution> = {}
    this.executions.forEach((execution, toolId) => {
      states[toolId] = execution
    })
    return states
  }
}

// Create singleton instance
export const toolManager = new ToolManager()

// Hook for React components
export function useToolExecution(toolId: string) {
  const [state, setState] = React.useState<ToolExecution | null>(
    toolManager.getExecutionState(toolId)
  )

  React.useEffect(() => {
    const unsubscribe = toolManager.subscribe(toolId, setState)
    return unsubscribe
  }, [toolId])

  return {
    canExecute: toolManager.canExecute(toolId),
    isExecuting: toolManager.isExecuting(toolId),
    remainingCooldown: toolManager.getRemainingCooldown(toolId),
    remainingTimeFormatted: toolManager.formatRemainingTime(toolId),
    startExecution: (customCooldown?: number) => toolManager.startExecution(toolId, customCooldown),
    finishExecution: () => toolManager.finishExecution(toolId),
    state
  }
}

import React from 'react'
