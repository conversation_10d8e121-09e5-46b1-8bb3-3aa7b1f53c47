import React, { useState, useEffect } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from './components/ui/tabs'
import { ThemeProvider } from './components/theme-provider'
import { TitleBar } from './components/title-bar'
import { SplashScreen } from './components/splash-screen'
import { Dashboard } from './pages/Dashboard'
import { CacheCleaner } from './pages/CacheCleaner'
import { GameManager } from './pages/GameManager'
import { SystemOptimizer } from './pages/SystemOptimizer'
import { SystemHealth } from './pages/SystemHealth'
import { Settings } from './pages/Settings'
import { About } from './pages/About'
import { Toaster } from './components/ui/toaster'
import { Button } from './components/ui/button'
import { Logo } from './components/logo'
import {
  Home,
  Trash2,
  Gamepad2,
  Settings as SettingsIcon,
  Activity,
  Zap,
  Info,
  Menu,
  X
} from 'lucide-react'

function App() {
  const [activeTab, setActiveTab] = useState('dashboard')
  const [isLoading, setIsLoading] = useState(true)
  const [showSplash, setShowSplash] = useState(true)
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)

  useEffect(() => {
    // Initialize app
    console.log('TurboBoost Pro initialized')
  }, [])

  const handleSplashComplete = () => {
    setShowSplash(false)
    setIsLoading(false)
  }

  return (
    <ThemeProvider defaultTheme="dark" storageKey="winoptimizer-theme">
      <div className="h-screen flex flex-col bg-background text-foreground">
        {showSplash && <SplashScreen onComplete={handleSplashComplete} />}

        <TitleBar />

        <div className="flex-1 flex overflow-hidden">
          <Tabs
            value={activeTab}
            onValueChange={(value) => {
              setActiveTab(value)
              setIsMobileMenuOpen(false) // Close mobile menu when tab changes
            }}
            className="flex-1 flex"
            orientation="vertical"
          >
            {/* Mobile Menu Button */}
            <div className="lg:hidden fixed top-16 left-4 z-50">
              <Button
                variant="outline"
                size="icon"
                onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                className="bg-background/80 backdrop-blur-sm"
              >
                {isMobileMenuOpen ? <X size={18} /> : <Menu size={18} />}
              </Button>
            </div>

            {/* Sidebar */}
            <div className={`
              w-64 bg-card border-r border-border flex flex-col
              lg:relative lg:translate-x-0
              fixed inset-y-0 left-0 z-40 transform transition-transform duration-300 ease-in-out
              ${isMobileMenuOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'}
            `}>
              {/* Mobile Overlay */}
              {isMobileMenuOpen && (
                <div
                  className="lg:hidden fixed inset-0 bg-black/50 z-30"
                  onClick={() => setIsMobileMenuOpen(false)}
                />
              )}

              <div className="p-4 border-b border-border">
                <Logo size={32} showText={true} />
                <p className="text-sm text-muted-foreground mt-2">
                  System Optimization Tool
                </p>
              </div>
              
              <TabsList className="flex-col h-auto bg-transparent p-2 space-y-1">
                <TabsTrigger 
                  value="dashboard" 
                  className="w-full justify-start gap-3 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
                >
                  <Home size={18} />
                  Dashboard
                </TabsTrigger>
                
                <TabsTrigger 
                  value="cache-cleaner" 
                  className="w-full justify-start gap-3 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
                >
                  <Trash2 size={18} />
                  Cache Cleaner
                </TabsTrigger>
                
                <TabsTrigger 
                  value="game-manager" 
                  className="w-full justify-start gap-3 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
                >
                  <Gamepad2 size={18} />
                  Game Manager
                </TabsTrigger>
                
                <TabsTrigger 
                  value="system-optimizer" 
                  className="w-full justify-start gap-3 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
                >
                  <Zap size={18} />
                  System Optimizer
                </TabsTrigger>
                
                <TabsTrigger 
                  value="system-health" 
                  className="w-full justify-start gap-3 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
                >
                  <Activity size={18} />
                  System Health
                </TabsTrigger>
                
                <TabsTrigger 
                  value="settings" 
                  className="w-full justify-start gap-3 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
                >
                  <SettingsIcon size={18} />
                  Settings
                </TabsTrigger>
                
                <TabsTrigger 
                  value="about" 
                  className="w-full justify-start gap-3 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
                >
                  <Info size={18} />
                  About
                </TabsTrigger>
              </TabsList>
              
              {/* Footer */}
              <div className="mt-auto p-4 border-t border-border">
                <div className="text-xs text-muted-foreground text-center">
                  <p>Created by</p>
                  <p className="font-semibold">sulindvaas & claude & augment</p>
                </div>
              </div>
            </div>

            {/* Main Content */}
            <div className="flex-1 flex flex-col overflow-hidden lg:ml-0">
              <TabsContent value="dashboard" className="flex-1 m-0">
                <Dashboard />
              </TabsContent>

              <TabsContent value="cache-cleaner" className="flex-1 m-0">
                <CacheCleaner />
              </TabsContent>

              <TabsContent value="game-manager" className="flex-1 m-0">
                <GameManager />
              </TabsContent>

              <TabsContent value="system-optimizer" className="flex-1 m-0">
                <SystemOptimizer />
              </TabsContent>

              <TabsContent value="system-health" className="flex-1 m-0">
                <SystemHealth />
              </TabsContent>

              <TabsContent value="settings" className="flex-1 m-0">
                <Settings />
              </TabsContent>

              <TabsContent value="about" className="flex-1 m-0">
                <About />
              </TabsContent>
            </div>
          </Tabs>
        </div>
        
        <Toaster />
      </div>
    </ThemeProvider>
  )
}

export default App
