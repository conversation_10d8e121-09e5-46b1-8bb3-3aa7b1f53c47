import React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card'
import { Button } from '../components/ui/button'
import { 
  Heart, 
  Github, 
  Mail, 
  Globe, 
  Star,
  Coffee,
  Users,
  Code,
  Zap,
  Shield
} from 'lucide-react'

export function About() {
  const openLink = (url: string) => {
    // @ts-ignore
    window.electron?.shell?.openExternal(url)
  }

  const features = [
    {
      icon: <Zap className="h-5 w-5 text-yellow-500" />,
      title: "System Optimization",
      description: "Advanced Windows optimization tools"
    },
    {
      icon: <Shield className="h-5 w-5 text-green-500" />,
      title: "System Health",
      description: "Real-time system monitoring"
    },
    {
      icon: <Code className="h-5 w-5 text-blue-500" />,
      title: "Game Management",
      description: "Centralized game library"
    }
  ]

  const contributors = [
    {
      name: "sulindva<PERSON>",
      role: "Lead Developer & Designer",
      description: "Created the vision and core functionality"
    },
    {
      name: "<PERSON>",
      role: "AI Assistant",
      description: "Provided development assistance and code optimization"
    },
    {
      name: "Augment",
      role: "Development Platform",
      description: "Powered the development environment and tools"
    }
  ]

  return (
    <div className="flex-1 p-4 lg:p-6 space-y-4 lg:space-y-6 overflow-auto custom-scrollbar force-scrollbar pb-20 lg:pb-6">
      {/* Header */}
      <div className="text-center space-y-4">
        <div className="text-6xl mb-4">🚀</div>
        <h1 className="text-4xl font-bold gradient-primary bg-clip-text text-transparent">
          TurboBoost Pro
        </h1>
        <p className="text-xl text-muted-foreground">
          Modern Windows Optimization & System Management Tool
        </p>
        <div className="flex items-center justify-center gap-2 text-sm text-muted-foreground">
          <span>Version 1.0.0</span>
          <span>•</span>
          <span>Built with ❤️ for Windows users</span>
        </div>
      </div>

      {/* Features */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Star className="h-5 w-5" />
            Key Features
          </CardTitle>
          <CardDescription>
            What makes WinOptimizer Pro special
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {features.map((feature, index) => (
              <div key={index} className="text-center p-4 border rounded-lg">
                <div className="flex justify-center mb-3">
                  {feature.icon}
                </div>
                <h4 className="font-medium mb-2">{feature.title}</h4>
                <p className="text-sm text-muted-foreground">{feature.description}</p>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Contributors */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Contributors
          </CardTitle>
          <CardDescription>
            The amazing team behind WinOptimizer Pro
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {contributors.map((contributor, index) => (
              <div key={index} className="flex items-start gap-4 p-4 border rounded-lg">
                <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center">
                  <span className="text-lg font-bold text-primary">
                    {contributor.name.charAt(0).toUpperCase()}
                  </span>
                </div>
                <div className="flex-1">
                  <h4 className="font-medium">{contributor.name}</h4>
                  <p className="text-sm text-primary">{contributor.role}</p>
                  <p className="text-sm text-muted-foreground mt-1">{contributor.description}</p>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Technology Stack */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Code className="h-5 w-5" />
            Technology Stack
          </CardTitle>
          <CardDescription>
            Built with modern technologies for optimal performance
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center p-3 border rounded-lg">
              <div className="font-medium">Electron</div>
              <div className="text-sm text-muted-foreground">Desktop Framework</div>
            </div>
            <div className="text-center p-3 border rounded-lg">
              <div className="font-medium">React</div>
              <div className="text-sm text-muted-foreground">UI Library</div>
            </div>
            <div className="text-center p-3 border rounded-lg">
              <div className="font-medium">TypeScript</div>
              <div className="text-sm text-muted-foreground">Programming Language</div>
            </div>
            <div className="text-center p-3 border rounded-lg">
              <div className="font-medium">Tailwind CSS</div>
              <div className="text-sm text-muted-foreground">Styling</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Support */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Heart className="h-5 w-5 text-red-500" />
            Support the Project
          </CardTitle>
          <CardDescription>
            Help us continue developing amazing tools
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <p className="text-sm text-muted-foreground">
            WinOptimizer Pro is a passion project created to help Windows users optimize their systems. 
            If you find it useful, consider supporting our work!
          </p>
          
          <div className="flex flex-wrap gap-3">
            <Button 
              onClick={() => openLink('https://github.com/sulindvaas/winoptimizer-pro')}
              variant="outline"
              className="flex items-center gap-2"
            >
              <Github className="h-4 w-4" />
              Star on GitHub
            </Button>
            
            <Button 
              onClick={() => openLink('mailto:<EMAIL>')}
              variant="outline"
              className="flex items-center gap-2"
            >
              <Mail className="h-4 w-4" />
              Contact Us
            </Button>
            
            <Button 
              onClick={() => openLink('https://buymeacoffee.com/sulindvaas')}
              variant="outline"
              className="flex items-center gap-2"
            >
              <Coffee className="h-4 w-4" />
              Buy us a Coffee
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* License */}
      <Card>
        <CardHeader>
          <CardTitle>License & Legal</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="text-sm text-muted-foreground space-y-2">
            <p>
              <strong>License:</strong> MIT License - Free for personal and commercial use
            </p>
            <p>
              <strong>Privacy:</strong> WinOptimizer Pro respects your privacy. No personal data is collected 
              without your explicit consent.
            </p>
            <p>
              <strong>Disclaimer:</strong> This software is provided "as is" without warranty. 
              Always backup your system before making changes.
            </p>
          </div>
          
          <div className="pt-4 border-t">
            <p className="text-xs text-muted-foreground text-center">
              © 2024 WinOptimizer Pro. Created by sulindvaas with assistance from Claude & Augment.
              <br />
              All rights reserved. Windows is a trademark of Microsoft Corporation.
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Easter Egg */}
      <Card className="border-dashed border-primary/30">
        <CardContent className="pt-6 text-center">
          <div className="text-2xl mb-2">🎉</div>
          <p className="text-sm text-muted-foreground">
            Thank you for using WinOptimizer Pro! 
            <br />
            You're helping make Windows optimization accessible to everyone.
          </p>
        </CardContent>
      </Card>
    </div>
  )
}
