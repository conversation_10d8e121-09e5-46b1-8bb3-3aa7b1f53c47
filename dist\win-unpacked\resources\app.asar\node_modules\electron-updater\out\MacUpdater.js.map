{"version": 3, "file": "MacUpdater.js", "sourceRoot": "", "sources": ["../src/MacUpdater.ts"], "names": [], "mappings": ";;;AAAA,+DAAqF;AACrF,uCAAyD;AACzD,2BAAqC;AACrC,6BAA4B;AAC5B,+BAA4E;AAE5E,6CAAgE;AAGhE,mDAA+C;AAE/C,iDAA4C;AAC5C,mCAAoC;AAEpC,MAAa,UAAW,SAAQ,uBAAU;IAOxC,YAAY,OAA2B,EAAE,GAAgB;QACvD,KAAK,CAAC,OAAO,EAAE,GAAG,CAAC,CAAA;QAPJ,kBAAa,GAAgB,OAAO,CAAC,UAAU,CAAC,CAAC,WAAW,CAAA;QAErE,6BAAwB,GAAG,KAAK,CAAA;QAOtC,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC,OAAO,EAAE,EAAE,CAAC,EAAE;YAClC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;YACrB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,CAAC,CAAA;QACxB,CAAC,CAAC,CAAA;QACF,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC,mBAAmB,EAAE,GAAG,EAAE;YAC9C,IAAI,CAAC,wBAAwB,GAAG,IAAI,CAAA;YACpC,IAAI,CAAC,KAAK,CAAC,iCAAiC,CAAC,CAAA;QAC/C,CAAC,CAAC,CAAA;IACJ,CAAC;IAEO,KAAK,CAAC,OAAe;QAC3B,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,IAAI,IAAI,EAAE,CAAC;YAC/B,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,CAAA;QAC7B,CAAC;IACH,CAAC;IAEO,mBAAmB;QACzB,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YAChB,IAAI,CAAC,KAAK,CAAC,sBAAsB,CAAC,CAAA;YAClC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;gBACtB,IAAI,GAAG,EAAE,CAAC;oBACR,IAAI,CAAC,KAAK,CAAC,kGAAkG,CAAC,CAAA;gBAChH,CAAC;YACH,CAAC,CAAC,CAAA;QACJ,CAAC;IACH,CAAC;IAES,KAAK,CAAC,gBAAgB,CAAC,qBAA4C;QAC3E,IAAI,KAAK,GAAG,qBAAqB,CAAC,qBAAqB,CAAC,QAAQ,CAAC,YAAY,CAAC,qBAAqB,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAA;QAE/H,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAA;QAExB,oDAAoD;QACpD,MAAM,oBAAoB,GAAG,wBAAwB,CAAA;QACrD,IAAI,SAAS,GAAG,KAAK,CAAA;QACrB,IAAI,CAAC;YACH,IAAI,CAAC,KAAK,CAAC,wCAAwC,CAAC,CAAA;YACpD,MAAM,MAAM,GAAG,IAAA,4BAAY,EAAC,QAAQ,EAAE,CAAC,oBAAoB,CAAC,EAAE,EAAE,QAAQ,EAAE,MAAM,EAAE,CAAC,CAAA;YACnF,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC,GAAG,oBAAoB,KAAK,CAAC,CAAA;YACzD,GAAG,CAAC,IAAI,CAAC,oDAAoD,SAAS,GAAG,CAAC,CAAA;QAC5E,CAAC;QAAC,OAAO,CAAM,EAAE,CAAC;YAChB,GAAG,CAAC,IAAI,CAAC,uEAAuE,CAAC,EAAE,CAAC,CAAA;QACtF,CAAC;QAED,IAAI,UAAU,GAAG,KAAK,CAAA;QACtB,IAAI,CAAC;YACH,IAAI,CAAC,KAAK,CAAC,6BAA6B,CAAC,CAAA;YACzC,MAAM,MAAM,GAAG,IAAA,4BAAY,EAAC,OAAO,EAAE,CAAC,IAAI,CAAC,EAAE,EAAE,QAAQ,EAAE,MAAM,EAAE,CAAC,CAAA;YAClE,MAAM,KAAK,GAAG,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAA;YACpC,GAAG,CAAC,IAAI,CAAC,6BAA6B,KAAK,EAAE,CAAC,CAAA;YAC9C,UAAU,GAAG,UAAU,IAAI,KAAK,CAAA;QAClC,CAAC;QAAC,OAAO,CAAM,EAAE,CAAC;YAChB,GAAG,CAAC,IAAI,CAAC,kDAAkD,CAAC,EAAE,CAAC,CAAA;QACjE,CAAC;QAED,UAAU,GAAG,UAAU,IAAI,OAAO,CAAC,IAAI,KAAK,OAAO,IAAI,SAAS,CAAA;QAEhE,2HAA2H;QAC3H,MAAM,OAAO,GAAG,CAAC,IAA4B,EAAE,EAAE,WAAC,OAAA,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAI,MAAA,IAAI,CAAC,IAAI,CAAC,GAAG,0CAAE,QAAQ,CAAC,OAAO,CAAC,CAAA,CAAA,EAAA,CAAA;QACzH,IAAI,UAAU,IAAI,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;YACtC,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,UAAU,KAAK,OAAO,CAAC,IAAI,CAAC,CAAC,CAAA;QAC5D,CAAC;aAAM,CAAC;YACN,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAA;QAC9C,CAAC;QAED,MAAM,WAAW,GAAG,IAAA,mBAAQ,EAAC,KAAK,EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,CAAA;QAE1D,IAAI,WAAW,IAAI,IAAI,EAAE,CAAC;YACxB,MAAM,IAAA,+BAAQ,EAAC,0BAA0B,IAAA,wCAAiB,EAAC,KAAK,CAAC,EAAE,EAAE,gCAAgC,CAAC,CAAA;QACxG,CAAC;QAED,MAAM,QAAQ,GAAG,qBAAqB,CAAC,qBAAqB,CAAC,QAAQ,CAAA;QACrE,MAAM,6BAA6B,GAAG,YAAY,CAAA;QAElD,OAAO,IAAI,CAAC,eAAe,CAAC;YAC1B,aAAa,EAAE,KAAK;YACpB,QAAQ,EAAE,WAAW;YACrB,qBAAqB;YACrB,IAAI,EAAE,KAAK,EAAE,eAAe,EAAE,eAAe,EAAE,EAAE;gBAC/C,MAAM,oBAAoB,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,sBAAuB,CAAC,QAAQ,EAAE,6BAA6B,CAAC,CAAA;gBAC5G,MAAM,uBAAuB,GAAG,GAAG,EAAE;oBACnC,IAAI,CAAC,IAAA,yBAAc,EAAC,oBAAoB,CAAC,EAAE,CAAC;wBAC1C,GAAG,CAAC,IAAI,CAAC,wHAAwH,CAAC,CAAA;wBAClI,OAAO,KAAK,CAAA;oBACd,CAAC;oBACD,OAAO,CAAC,qBAAqB,CAAC,2BAA2B,CAAA;gBAC3D,CAAC,CAAA;gBACD,IAAI,0BAA0B,GAAG,IAAI,CAAA;gBACrC,IAAI,uBAAuB,EAAE,EAAE,CAAC;oBAC9B,0BAA0B,GAAG,MAAM,IAAI,CAAC,6BAA6B,CAAC,WAAW,EAAE,qBAAqB,EAAE,eAAe,EAAE,QAAQ,EAAE,6BAA6B,CAAC,CAAA;gBACrK,CAAC;gBAED,IAAI,0BAA0B,EAAE,CAAC;oBAC/B,MAAM,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,WAAW,CAAC,GAAG,EAAE,eAAe,EAAE,eAAe,CAAC,CAAA;gBACrF,CAAC;YACH,CAAC;YACD,IAAI,EAAE,KAAK,EAAC,KAAK,EAAC,EAAE;gBAClB,IAAI,CAAC,qBAAqB,CAAC,2BAA2B,EAAE,CAAC;oBACvD,IAAI,CAAC;wBACH,MAAM,oBAAoB,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,sBAAuB,CAAC,QAAQ,EAAE,6BAA6B,CAAC,CAAA;wBAC5G,MAAM,IAAA,mBAAQ,EAAC,KAAK,CAAC,cAAc,EAAE,oBAAoB,CAAC,CAAA;oBAC5D,CAAC;oBAAC,OAAO,KAAU,EAAE,CAAC;wBACpB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,sEAAsE,KAAK,CAAC,OAAO,EAAE,CAAC,CAAA;oBAC1G,CAAC;gBACH,CAAC;gBACD,OAAO,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE,KAAK,CAAC,CAAA;YAClD,CAAC;SACF,CAAC,CAAA;IACJ,CAAC;IAEO,KAAK,CAAC,gBAAgB,CAAC,WAAmC,EAAE,KAA4B;;QAC9F,MAAM,cAAc,GAAG,KAAK,CAAC,cAAc,CAAA;QAC3C,MAAM,cAAc,GAAG,MAAA,WAAW,CAAC,IAAI,CAAC,IAAI,mCAAI,CAAC,MAAM,IAAA,eAAI,EAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAA;QAEjF,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAA;QACxB,MAAM,UAAU,GAAG,eAAe,WAAW,CAAC,GAAG,CAAC,IAAI,EAAE,CAAA;QACxD,IAAI,CAAC,mBAAmB,EAAE,CAAA;QAC1B,IAAI,CAAC,KAAK,CAAC,kDAAkD,UAAU,GAAG,CAAC,CAAA;QAC3E,IAAI,CAAC,MAAM,GAAG,IAAA,mBAAY,GAAE,CAAA;QAC5B,IAAI,CAAC,KAAK,CAAC,oDAAoD,UAAU,GAAG,CAAC,CAAA;QAC7E,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,GAAG,EAAE;YAC3B,GAAG,CAAC,IAAI,CAAC,mDAAmD,UAAU,GAAG,CAAC,CAAA;QAC5E,CAAC,CAAC,CAAA;QAEF,sEAAsE;QACtE,MAAM,YAAY,GAAG,CAAC,CAAS,EAAU,EAAE;YACzC,MAAM,OAAO,GAAG,CAAC,CAAC,OAAO,EAAE,CAAA;YAC3B,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE,CAAC;gBAChC,OAAO,OAAO,CAAA;YAChB,CAAC;YACD,OAAO,oBAAoB,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI,EAAE,CAAA;QAC5C,CAAC,CAAA;QAED,OAAO,MAAM,IAAI,OAAO,CAAgB,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YAC1D,MAAM,IAAI,GAAG,IAAA,oBAAW,EAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAA;YACvF,MAAM,QAAQ,GAAG,MAAM,CAAC,IAAI,CAAC,eAAe,IAAI,EAAE,EAAE,OAAO,CAAC,CAAA;YAE5D,wBAAwB;YACxB,MAAM,OAAO,GAAG,IAAI,IAAA,oBAAW,EAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAA;YACzD,IAAI,CAAC,MAAO,CAAC,EAAE,CAAC,SAAS,EAAE,CAAC,OAAwB,EAAE,QAAwB,EAAE,EAAE;gBAChF,MAAM,UAAU,GAAG,OAAO,CAAC,GAAI,CAAA;gBAC/B,GAAG,CAAC,IAAI,CAAC,GAAG,UAAU,YAAY,CAAC,CAAA;gBACnC,IAAI,UAAU,KAAK,GAAG,EAAE,CAAC;oBACvB,8BAA8B;oBAC9B,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,aAAa,IAAI,OAAO,CAAC,OAAO,CAAC,aAAa,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;wBAC7F,QAAQ,CAAC,UAAU,GAAG,GAAG,CAAA;wBACzB,QAAQ,CAAC,aAAa,GAAG,oCAAoC,CAAA;wBAC7D,QAAQ,CAAC,GAAG,EAAE,CAAA;wBACd,GAAG,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAA;wBACnC,OAAM;oBACR,CAAC;oBAED,0BAA0B;oBAC1B,MAAM,iBAAiB,GAAG,OAAO,CAAC,OAAO,CAAC,aAAa,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA;oBACrE,MAAM,WAAW,GAAG,MAAM,CAAC,IAAI,CAAC,iBAAiB,EAAE,QAAQ,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAA;oBAC9E,MAAM,CAAC,QAAQ,EAAE,QAAQ,CAAC,GAAG,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;oBACnD,IAAI,QAAQ,KAAK,aAAa,IAAI,QAAQ,KAAK,IAAI,EAAE,CAAC;wBACpD,QAAQ,CAAC,UAAU,GAAG,GAAG,CAAA;wBACzB,QAAQ,CAAC,aAAa,GAAG,oCAAoC,CAAA;wBAC7D,QAAQ,CAAC,GAAG,EAAE,CAAA;wBACd,GAAG,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAA;wBAC/C,OAAM;oBACR,CAAC;oBAED,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,aAAa,YAAY,CAAC,IAAI,CAAC,MAAO,CAAC,GAAG,OAAO,KAAK,CAAC,CAAA;oBAChF,QAAQ,CAAC,SAAS,CAAC,GAAG,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE,gBAAgB,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC,CAAA;oBAC9F,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,CAAA;oBAClB,OAAM;gBACR,CAAC;gBAED,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE,CAAC;oBACpC,GAAG,CAAC,IAAI,CAAC,GAAG,UAAU,+BAA+B,CAAC,CAAA;oBACtD,QAAQ,CAAC,SAAS,CAAC,GAAG,CAAC,CAAA;oBACvB,QAAQ,CAAC,GAAG,EAAE,CAAA;oBACd,OAAM;gBACR,CAAC;gBAED,GAAG,CAAC,IAAI,CAAC,GAAG,OAAO,oCAAoC,cAAc,EAAE,CAAC,CAAA;gBAExE,IAAI,aAAa,GAAG,KAAK,CAAA;gBACzB,QAAQ,CAAC,EAAE,CAAC,QAAQ,EAAE,GAAG,EAAE;oBACzB,IAAI,CAAC,aAAa,EAAE,CAAC;wBACnB,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,OAAO,EAAE,MAAM,CAAC,CAAA;wBAClD,OAAO,CAAC,EAAE,CAAC,CAAA;oBACb,CAAC;gBACH,CAAC,CAAC,CAAA;gBAEF,MAAM,UAAU,GAAG,IAAA,qBAAgB,EAAC,cAAc,CAAC,CAAA;gBACnD,UAAU,CAAC,EAAE,CAAC,OAAO,EAAE,KAAK,CAAC,EAAE;oBAC7B,IAAI,CAAC;wBACH,QAAQ,CAAC,GAAG,EAAE,CAAA;oBAChB,CAAC;oBAAC,OAAO,CAAM,EAAE,CAAC;wBAChB,GAAG,CAAC,IAAI,CAAC,wBAAwB,CAAC,EAAE,CAAC,CAAA;oBACvC,CAAC;oBACD,aAAa,GAAG,IAAI,CAAA;oBACpB,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,OAAO,EAAE,MAAM,CAAC,CAAA;oBAClD,MAAM,CAAC,IAAI,KAAK,CAAC,gBAAgB,cAAc,MAAM,KAAK,EAAE,CAAC,CAAC,CAAA;gBAChE,CAAC,CAAC,CAAA;gBAEF,QAAQ,CAAC,SAAS,CAAC,GAAG,EAAE;oBACtB,cAAc,EAAE,iBAAiB;oBACjC,gBAAgB,EAAE,cAAc;iBACjC,CAAC,CAAA;gBACF,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;YAC3B,CAAC,CAAC,CAAA;YAEF,IAAI,CAAC,KAAK,CAAC,+DAA+D,UAAU,GAAG,CAAC,CAAA;YAExF,IAAI,CAAC,MAAO,CAAC,MAAM,CAAC,CAAC,EAAE,WAAW,EAAE,GAAG,EAAE;gBACvC,IAAI,CAAC,KAAK,CAAC,8DAA8D,YAAY,CAAC,IAAI,CAAC,MAAO,CAAC,KAAK,UAAU,GAAG,CAAC,CAAA;gBACtH,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC;oBAC5B,GAAG,EAAE,YAAY,CAAC,IAAI,CAAC,MAAO,CAAC;oBAC/B,OAAO,EAAE;wBACP,eAAe,EAAE,UAAU;wBAC3B,aAAa,EAAE,SAAS,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE;qBACtD;iBACF,CAAC,CAAA;gBAEF,uEAAuE;gBACvE,IAAI,CAAC,wBAAwB,CAAC,KAAK,CAAC,CAAA;gBAEpC,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;oBAC9B,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM,CAAC,CAAA;oBACxC,sEAAsE;oBACtE,IAAI,CAAC,aAAa,CAAC,eAAe,EAAE,CAAA;gBACtC,CAAC;qBAAM,CAAC;oBACN,OAAO,CAAC,EAAE,CAAC,CAAA;gBACb,CAAC;YACH,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;IACJ,CAAC;IAEO,sBAAsB;QAC5B,IAAI,IAAI,CAAC,sBAAsB,EAAE,CAAC;YAChC,IAAI,CAAC,aAAa,CAAC,cAAc,EAAE,CAAA;QACrC,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAA;QACjB,CAAC;QACD,IAAI,CAAC,mBAAmB,EAAE,CAAA;IAC5B,CAAC;IAED,cAAc;QACZ,IAAI,IAAI,CAAC,wBAAwB,EAAE,CAAC;YAClC,4DAA4D;YAC5D,IAAI,CAAC,sBAAsB,EAAE,CAAA;QAC/B,CAAC;aAAM,CAAC;YACN,sDAAsD;YACtD,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC,mBAAmB,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,sBAAsB,EAAE,CAAC,CAAA;YAE/E,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE,CAAC;gBAC/B;;;mBAGG;gBACH,IAAI,CAAC,aAAa,CAAC,eAAe,EAAE,CAAA;YACtC,CAAC;QACH,CAAC;IACH,CAAC;CACF;AA5QD,gCA4QC", "sourcesContent": ["import { AllPublishOptions, newError, safeStringify<PERSON>son } from \"builder-util-runtime\"\nimport { pathExistsSync, stat, copyFile } from \"fs-extra\"\nimport { createReadStream } from \"fs\"\nimport * as path from \"path\"\nimport { createServer, IncomingMessage, Server, ServerResponse } from \"http\"\nimport { AppAdapter } from \"./AppAdapter\"\nimport { AppUpdater, DownloadUpdateOptions } from \"./AppUpdater\"\nimport { ResolvedUpdateFileInfo } from \"./main\"\nimport { UpdateDownloadedEvent } from \"./types\"\nimport { findFile } from \"./providers/Provider\"\nimport AutoUpdater = Electron.AutoUpdater\nimport { execFileSync } from \"child_process\"\nimport { randomBytes } from \"crypto\"\n\nexport class MacUpdater extends AppUpdater {\n  private readonly nativeUpdater: AutoUpdater = require(\"electron\").autoUpdater\n\n  private squirrelDownloadedUpdate = false\n\n  private server?: Server\n\n  constructor(options?: AllPublishOptions, app?: AppAdapter) {\n    super(options, app)\n\n    this.nativeUpdater.on(\"error\", it => {\n      this._logger.warn(it)\n      this.emit(\"error\", it)\n    })\n    this.nativeUpdater.on(\"update-downloaded\", () => {\n      this.squirrelDownloadedUpdate = true\n      this.debug(\"nativeUpdater.update-downloaded\")\n    })\n  }\n\n  private debug(message: string): void {\n    if (this._logger.debug != null) {\n      this._logger.debug(message)\n    }\n  }\n\n  private closeServerIfExists() {\n    if (this.server) {\n      this.debug(\"Closing proxy server\")\n      this.server.close(err => {\n        if (err) {\n          this.debug(\"proxy server wasn't already open, probably attempted closing again as a safety check before quit\")\n        }\n      })\n    }\n  }\n\n  protected async doDownloadUpdate(downloadUpdateOptions: DownloadUpdateOptions): Promise<Array<string>> {\n    let files = downloadUpdateOptions.updateInfoAndProvider.provider.resolveFiles(downloadUpdateOptions.updateInfoAndProvider.info)\n\n    const log = this._logger\n\n    // detect if we are running inside Rosetta emulation\n    const sysctlRosettaInfoKey = \"sysctl.proc_translated\"\n    let isRosetta = false\n    try {\n      this.debug(\"Checking for macOS Rosetta environment\")\n      const result = execFileSync(\"sysctl\", [sysctlRosettaInfoKey], { encoding: \"utf8\" })\n      isRosetta = result.includes(`${sysctlRosettaInfoKey}: 1`)\n      log.info(`Checked for macOS Rosetta environment (isRosetta=${isRosetta})`)\n    } catch (e: any) {\n      log.warn(`sysctl shell command to check for macOS Rosetta environment failed: ${e}`)\n    }\n\n    let isArm64Mac = false\n    try {\n      this.debug(\"Checking for arm64 in uname\")\n      const result = execFileSync(\"uname\", [\"-a\"], { encoding: \"utf8\" })\n      const isArm = result.includes(\"ARM\")\n      log.info(`Checked 'uname -a': arm64=${isArm}`)\n      isArm64Mac = isArm64Mac || isArm\n    } catch (e: any) {\n      log.warn(`uname shell command to check for arm64 failed: ${e}`)\n    }\n\n    isArm64Mac = isArm64Mac || process.arch === \"arm64\" || isRosetta\n\n    // allow arm64 macs to install universal or rosetta2(x64) - https://github.com/electron-userland/electron-builder/pull/5524\n    const isArm64 = (file: ResolvedUpdateFileInfo) => file.url.pathname.includes(\"arm64\") || file.info.url?.includes(\"arm64\")\n    if (isArm64Mac && files.some(isArm64)) {\n      files = files.filter(file => isArm64Mac === isArm64(file))\n    } else {\n      files = files.filter(file => !isArm64(file))\n    }\n\n    const zipFileInfo = findFile(files, \"zip\", [\"pkg\", \"dmg\"])\n\n    if (zipFileInfo == null) {\n      throw newError(`ZIP file not provided: ${safeStringifyJson(files)}`, \"ERR_UPDATER_ZIP_FILE_NOT_FOUND\")\n    }\n\n    const provider = downloadUpdateOptions.updateInfoAndProvider.provider\n    const CURRENT_MAC_APP_ZIP_FILE_NAME = \"update.zip\"\n\n    return this.executeDownload({\n      fileExtension: \"zip\",\n      fileInfo: zipFileInfo,\n      downloadUpdateOptions,\n      task: async (destinationFile, downloadOptions) => {\n        const cachedUpdateFilePath = path.join(this.downloadedUpdateHelper!.cacheDir, CURRENT_MAC_APP_ZIP_FILE_NAME)\n        const canDifferentialDownload = () => {\n          if (!pathExistsSync(cachedUpdateFilePath)) {\n            log.info(\"Unable to locate previous update.zip for differential download (is this first install?), falling back to full download\")\n            return false\n          }\n          return !downloadUpdateOptions.disableDifferentialDownload\n        }\n        let differentialDownloadFailed = true\n        if (canDifferentialDownload()) {\n          differentialDownloadFailed = await this.differentialDownloadInstaller(zipFileInfo, downloadUpdateOptions, destinationFile, provider, CURRENT_MAC_APP_ZIP_FILE_NAME)\n        }\n\n        if (differentialDownloadFailed) {\n          await this.httpExecutor.download(zipFileInfo.url, destinationFile, downloadOptions)\n        }\n      },\n      done: async event => {\n        if (!downloadUpdateOptions.disableDifferentialDownload) {\n          try {\n            const cachedUpdateFilePath = path.join(this.downloadedUpdateHelper!.cacheDir, CURRENT_MAC_APP_ZIP_FILE_NAME)\n            await copyFile(event.downloadedFile, cachedUpdateFilePath)\n          } catch (error: any) {\n            this._logger.warn(`Unable to copy file for caching for future differential downloads: ${error.message}`)\n          }\n        }\n        return this.updateDownloaded(zipFileInfo, event)\n      },\n    })\n  }\n\n  private async updateDownloaded(zipFileInfo: ResolvedUpdateFileInfo, event: UpdateDownloadedEvent): Promise<Array<string>> {\n    const downloadedFile = event.downloadedFile\n    const updateFileSize = zipFileInfo.info.size ?? (await stat(downloadedFile)).size\n\n    const log = this._logger\n    const logContext = `fileToProxy=${zipFileInfo.url.href}`\n    this.closeServerIfExists()\n    this.debug(`Creating proxy server for native Squirrel.Mac (${logContext})`)\n    this.server = createServer()\n    this.debug(`Proxy server for native Squirrel.Mac is created (${logContext})`)\n    this.server.on(\"close\", () => {\n      log.info(`Proxy server for native Squirrel.Mac is closed (${logContext})`)\n    })\n\n    // must be called after server is listening, otherwise address is null\n    const getServerUrl = (s: Server): string => {\n      const address = s.address()\n      if (typeof address === \"string\") {\n        return address\n      }\n      return `http://127.0.0.1:${address?.port}`\n    }\n\n    return await new Promise<Array<string>>((resolve, reject) => {\n      const pass = randomBytes(64).toString(\"base64\").replace(/\\//g, \"_\").replace(/\\+/g, \"-\")\n      const authInfo = Buffer.from(`autoupdater:${pass}`, \"ascii\")\n\n      // insecure random is ok\n      const fileUrl = `/${randomBytes(64).toString(\"hex\")}.zip`\n      this.server!.on(\"request\", (request: IncomingMessage, response: ServerResponse) => {\n        const requestUrl = request.url!\n        log.info(`${requestUrl} requested`)\n        if (requestUrl === \"/\") {\n          // check for basic auth header\n          if (!request.headers.authorization || request.headers.authorization.indexOf(\"Basic \") === -1) {\n            response.statusCode = 401\n            response.statusMessage = \"Invalid Authentication Credentials\"\n            response.end()\n            log.warn(\"No authenthication info\")\n            return\n          }\n\n          // verify auth credentials\n          const base64Credentials = request.headers.authorization.split(\" \")[1]\n          const credentials = Buffer.from(base64Credentials, \"base64\").toString(\"ascii\")\n          const [username, password] = credentials.split(\":\")\n          if (username !== \"autoupdater\" || password !== pass) {\n            response.statusCode = 401\n            response.statusMessage = \"Invalid Authentication Credentials\"\n            response.end()\n            log.warn(\"Invalid authenthication credentials\")\n            return\n          }\n\n          const data = Buffer.from(`{ \"url\": \"${getServerUrl(this.server!)}${fileUrl}\" }`)\n          response.writeHead(200, { \"Content-Type\": \"application/json\", \"Content-Length\": data.length })\n          response.end(data)\n          return\n        }\n\n        if (!requestUrl.startsWith(fileUrl)) {\n          log.warn(`${requestUrl} requested, but not supported`)\n          response.writeHead(404)\n          response.end()\n          return\n        }\n\n        log.info(`${fileUrl} requested by Squirrel.Mac, pipe ${downloadedFile}`)\n\n        let errorOccurred = false\n        response.on(\"finish\", () => {\n          if (!errorOccurred) {\n            this.nativeUpdater.removeListener(\"error\", reject)\n            resolve([])\n          }\n        })\n\n        const readStream = createReadStream(downloadedFile)\n        readStream.on(\"error\", error => {\n          try {\n            response.end()\n          } catch (e: any) {\n            log.warn(`cannot end response: ${e}`)\n          }\n          errorOccurred = true\n          this.nativeUpdater.removeListener(\"error\", reject)\n          reject(new Error(`Cannot pipe \"${downloadedFile}\": ${error}`))\n        })\n\n        response.writeHead(200, {\n          \"Content-Type\": \"application/zip\",\n          \"Content-Length\": updateFileSize,\n        })\n        readStream.pipe(response)\n      })\n\n      this.debug(`Proxy server for native Squirrel.Mac is starting to listen (${logContext})`)\n\n      this.server!.listen(0, \"127.0.0.1\", () => {\n        this.debug(`Proxy server for native Squirrel.Mac is listening (address=${getServerUrl(this.server!)}, ${logContext})`)\n        this.nativeUpdater.setFeedURL({\n          url: getServerUrl(this.server!),\n          headers: {\n            \"Cache-Control\": \"no-cache\",\n            Authorization: `Basic ${authInfo.toString(\"base64\")}`,\n          },\n        })\n\n        // The update has been downloaded and is ready to be served to Squirrel\n        this.dispatchUpdateDownloaded(event)\n\n        if (this.autoInstallOnAppQuit) {\n          this.nativeUpdater.once(\"error\", reject)\n          // This will trigger fetching and installing the file on Squirrel side\n          this.nativeUpdater.checkForUpdates()\n        } else {\n          resolve([])\n        }\n      })\n    })\n  }\n\n  private handleUpdateDownloaded() {\n    if (this.autoRunAppAfterInstall) {\n      this.nativeUpdater.quitAndInstall()\n    } else {\n      this.app.quit()\n    }\n    this.closeServerIfExists()\n  }\n\n  quitAndInstall(): void {\n    if (this.squirrelDownloadedUpdate) {\n      // update already fetched by Squirrel, it's ready to install\n      this.handleUpdateDownloaded()\n    } else {\n      // Quit and install as soon as Squirrel get the update\n      this.nativeUpdater.on(\"update-downloaded\", () => this.handleUpdateDownloaded())\n\n      if (!this.autoInstallOnAppQuit) {\n        /**\n         * If this was not `true` previously then MacUpdater.doDownloadUpdate()\n         * would not actually initiate the downloading by electron's autoUpdater\n         */\n        this.nativeUpdater.checkForUpdates()\n      }\n    }\n  }\n}\n"]}