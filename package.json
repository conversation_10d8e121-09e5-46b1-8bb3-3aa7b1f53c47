{"name": "turboboost-pro", "version": "1.0.0", "description": "Modern Windows optimization and system management tool with TurboBoost technology", "main": "dist-electron/main/index.js", "scripts": {"dev": "electron-vite dev", "build": "electron-vite build", "preview": "electron-vite preview", "build:win": "npm run build && electron-builder", "build:dir": "npm run build && electron-builder --dir", "test": "jest", "test:e2e": "playwright test", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "typecheck": "tsc --noEmit"}, "keywords": ["windows", "optimization", "system-cleaner", "game-manager", "desktop-app"], "author": "sulind<PERSON><PERSON> & claude & augment", "license": "MIT", "devDependencies": {"@electron/rebuild": "^3.4.0", "@playwright/test": "^1.40.0", "@types/jest": "^29.5.14", "@types/node": "^20.9.0", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vitejs/plugin-react": "^4.1.1", "autoprefixer": "^10.4.16", "electron": "^27.1.2", "electron-builder": "^24.6.4", "electron-vite": "^1.0.29", "eslint": "^8.53.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "jest": "^29.7.0", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "ts-jest": "^29.3.4", "typescript": "^5.2.2", "vite": "^4.5.0"}, "dependencies": {"@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "electron-updater": "^6.1.4", "lucide-react": "^0.292.0", "react": "^18.2.0", "react-dom": "^18.2.0", "tailwind-merge": "^2.0.0", "tailwindcss-animate": "^1.0.7"}, "build": {"appId": "com.sulindvaas.turboboost", "productName": "TurboBoost Pro", "directories": {"output": "dist"}, "files": ["dist-electron", "dist"], "win": {"target": [{"target": "nsis", "arch": ["x64"]}], "requestedExecutionLevel": "requireAdministrator"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true}}}