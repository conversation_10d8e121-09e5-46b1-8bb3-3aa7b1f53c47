@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Custom animations */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-slide-in {
  animation: slideIn 0.3s ease-out;
}

.animate-pulse-slow {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* ELECTRON SCROLLBAR - SUPER VISIBLE */
.custom-scrollbar {
  scrollbar-width: auto !important;
  scrollbar-color: #3B82F6 #E5E7EB !important;
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
  overflow-y: scroll !important;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 16px !important;
  height: 16px !important;
  background: #F3F4F6 !important;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #E5E7EB !important;
  border-radius: 8px !important;
  margin: 4px !important;
  border: 1px solid #D1D5DB !important;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, #3B82F6 0%, #2563EB 100%) !important;
  border-radius: 8px !important;
  border: 2px solid #E5E7EB !important;
  min-height: 30px !important;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, #2563EB 0%, #1D4ED8 100%) !important;
  border-color: #3B82F6 !important;
  box-shadow: 0 4px 8px rgba(0,0,0,0.2) !important;
}

.custom-scrollbar::-webkit-scrollbar-thumb:active {
  background: #1D4ED8 !important;
  transform: scale(0.95) !important;
}

.custom-scrollbar::-webkit-scrollbar-corner {
  background: #E5E7EB !important;
}

/* FORCE SCROLLBAR - ALWAYS VISIBLE */
.force-scrollbar {
  overflow-y: scroll !important;
  overflow-x: hidden !important;
}

.force-scrollbar::-webkit-scrollbar {
  width: 18px !important;
  background: #F9FAFB !important;
  border-left: 1px solid #E5E7EB !important;
}

.force-scrollbar::-webkit-scrollbar-track {
  background: #F3F4F6 !important;
  border-radius: 0 !important;
  margin: 0 !important;
}

.force-scrollbar::-webkit-scrollbar-thumb {
  background: #3B82F6 !important;
  border-radius: 9px !important;
  border: 3px solid #F3F4F6 !important;
  min-height: 40px !important;
}

.force-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #2563EB !important;
  border-color: #E5E7EB !important;
}

/* DARK MODE SCROLLBAR */
.dark .custom-scrollbar::-webkit-scrollbar {
  background: #1F2937 !important;
}

.dark .custom-scrollbar::-webkit-scrollbar-track {
  background: #374151 !important;
  border-color: #4B5563 !important;
}

.dark .custom-scrollbar::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, #60A5FA 0%, #3B82F6 100%) !important;
  border-color: #374151 !important;
}

.dark .force-scrollbar::-webkit-scrollbar {
  background: #1F2937 !important;
  border-left-color: #374151 !important;
}

.dark .force-scrollbar::-webkit-scrollbar-track {
  background: #374151 !important;
}

.dark .force-scrollbar::-webkit-scrollbar-thumb {
  background: #60A5FA !important;
  border-color: #374151 !important;
}

/* Glass effect */
.glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.dark .glass {
  background: rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Gradient backgrounds */
.gradient-primary {
  background: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--primary)) 100%);
}

.gradient-secondary {
  background: linear-gradient(135deg, hsl(var(--secondary)) 0%, hsl(var(--muted)) 100%);
}

/* Card hover effects */
.card-hover {
  transition: all 0.2s ease-in-out;
}

.card-hover:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.dark .card-hover:hover {
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

/* Button animations */
.btn-animate {
  transition: all 0.2s ease-in-out;
}

.btn-animate:hover {
  transform: scale(1.02);
}

.btn-animate:active {
  transform: scale(0.98);
}

/* Progress bar animations */
.progress-bar {
  transition: width 0.3s ease-in-out;
}

/* Loading states */
.loading-dots::after {
  content: '';
  animation: dots 1.5s steps(5, end) infinite;
}

@keyframes dots {
  0%, 20% {
    color: rgba(0,0,0,0);
    text-shadow:
      .25em 0 0 rgba(0,0,0,0),
      .5em 0 0 rgba(0,0,0,0);
  }
  40% {
    color: hsl(var(--foreground));
    text-shadow:
      .25em 0 0 rgba(0,0,0,0),
      .5em 0 0 rgba(0,0,0,0);
  }
  60% {
    text-shadow:
      .25em 0 0 hsl(var(--foreground)),
      .5em 0 0 rgba(0,0,0,0);
  }
  80%, 100% {
    text-shadow:
      .25em 0 0 hsl(var(--foreground)),
      .5em 0 0 hsl(var(--foreground));
  }
}

/* Loading spinner */
.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid hsl(var(--muted));
  border-top: 4px solid hsl(var(--primary));
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Splash screen */
.splash-screen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, hsl(var(--background)) 0%, hsl(var(--muted)) 100%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  transition: opacity 0.5s ease-out;
}

.splash-screen.fade-out {
  opacity: 0;
  pointer-events: none;
}

/* Tool execution states */
.tool-executing {
  opacity: 0.7;
  pointer-events: none;
}

.tool-cooldown {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Mobile optimizations */
@media (max-width: 1024px) {
  /* Ensure content is accessible on mobile */
  .mobile-safe-area {
    padding-bottom: env(safe-area-inset-bottom, 20px);
  }

  /* Better touch targets */
  button, .clickable {
    min-height: 44px;
    min-width: 44px;
  }

  /* Improved scrolling on mobile */
  .mobile-scroll {
    overscroll-behavior: contain;
    -webkit-overflow-scrolling: touch;
  }

  /* Hide scrollbars on mobile for cleaner look */
  .mobile-hide-scrollbar {
    scrollbar-width: none;
    -ms-overflow-style: none;
  }

  .mobile-hide-scrollbar::-webkit-scrollbar {
    display: none;
  }
}

/* Responsive text sizes */
@media (max-width: 640px) {
  .responsive-text-sm {
    font-size: 0.875rem;
  }

  .responsive-text-base {
    font-size: 1rem;
  }

  .responsive-text-lg {
    font-size: 1.125rem;
  }

  .responsive-text-xl {
    font-size: 1.25rem;
  }

  .responsive-text-2xl {
    font-size: 1.5rem;
  }
}

/* Fix for mobile viewport height */
@supports (-webkit-touch-callout: none) {
  .mobile-vh-fix {
    height: -webkit-fill-available;
  }
}

/* ELECTRON SPECIFIC SCROLLBAR FIXES */
body {
  overflow: hidden !important;
}

/* Make sure all containers have proper scrolling */
.electron-scrollable {
  overflow-y: auto !important;
  overflow-x: hidden !important;
  height: 100% !important;
}

.electron-scrollable::-webkit-scrollbar {
  width: 20px !important;
  background: #F3F4F6 !important;
}

.electron-scrollable::-webkit-scrollbar-track {
  background: #E5E7EB !important;
  border-radius: 10px !important;
  margin: 5px !important;
}

.electron-scrollable::-webkit-scrollbar-thumb {
  background: #3B82F6 !important;
  border-radius: 10px !important;
  border: 3px solid #E5E7EB !important;
  min-height: 50px !important;
}

.electron-scrollable::-webkit-scrollbar-thumb:hover {
  background: #2563EB !important;
}

/* Dark mode for electron scrollbar */
.dark .electron-scrollable::-webkit-scrollbar {
  background: #1F2937 !important;
}

.dark .electron-scrollable::-webkit-scrollbar-track {
  background: #374151 !important;
}

.dark .electron-scrollable::-webkit-scrollbar-thumb {
  background: #60A5FA !important;
  border-color: #374151 !important;
}

/* Slider component fixes */
[data-radix-slider-root] {
  position: relative;
  display: flex;
  align-items: center;
  user-select: none;
  touch-action: none;
  width: 100%;
  height: 20px;
}

[data-radix-slider-track] {
  background-color: hsl(var(--secondary));
  position: relative;
  flex-grow: 1;
  border-radius: 9999px;
  height: 8px;
}

[data-radix-slider-range] {
  position: absolute;
  background-color: hsl(var(--primary));
  border-radius: 9999px;
  height: 100%;
}

[data-radix-slider-thumb] {
  display: block;
  width: 20px;
  height: 20px;
  background-color: hsl(var(--background));
  border: 2px solid hsl(var(--primary));
  border-radius: 50%;
  transition: all 0.2s ease;
  cursor: pointer;
}

[data-radix-slider-thumb]:hover {
  scale: 1.1;
}

[data-radix-slider-thumb]:focus {
  outline: none;
  box-shadow: 0 0 0 2px hsl(var(--ring));
}
