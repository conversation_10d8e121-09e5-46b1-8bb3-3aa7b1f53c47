import React, { useState, useEffect } from 'react'
import { Progress } from './ui/progress'
import { Zap } from 'lucide-react'

interface SplashScreenProps {
  onComplete: () => void
}

export function SplashScreen({ onComplete }: SplashScreenProps) {
  const [progress, setProgress] = useState(0)
  const [currentTask, setCurrentTask] = useState('Initializing...')
  const [isVisible, setIsVisible] = useState(true)

  const tasks = [
    'Initializing application...',
    'Loading system modules...',
    'Scanning system health...',
    'Preparing optimization tools...',
    'Loading game database...',
    'Finalizing setup...'
  ]

  useEffect(() => {
    let currentTaskIndex = 0
    let progressValue = 0

    const interval = setInterval(() => {
      progressValue += Math.random() * 15 + 5 // Random progress between 5-20%
      
      if (progressValue >= 100) {
        progressValue = 100
        setProgress(100)
        setCurrentTask('Ready!')
        
        setTimeout(() => {
          setIsVisible(false)
          setTimeout(onComplete, 500) // Wait for fade out animation
        }, 800)
        
        clearInterval(interval)
        return
      }

      setProgress(progressValue)

      // Update task based on progress
      const taskProgress = progressValue / 100 * tasks.length
      const newTaskIndex = Math.min(Math.floor(taskProgress), tasks.length - 1)
      
      if (newTaskIndex !== currentTaskIndex) {
        currentTaskIndex = newTaskIndex
        setCurrentTask(tasks[currentTaskIndex])
      }
    }, 200 + Math.random() * 300) // Random interval between 200-500ms

    return () => clearInterval(interval)
  }, [onComplete])

  return (
    <div className={`splash-screen ${!isVisible ? 'fade-out' : ''}`}>
      <div className="text-center space-y-8">
        {/* Logo */}
        <div className="flex items-center justify-center space-x-3">
          <div className="p-4 bg-primary rounded-full">
            <Zap className="h-12 w-12 text-primary-foreground" />
          </div>
          <div>
            <h1 className="text-4xl font-bold gradient-primary bg-clip-text text-transparent">
              WinOptimizer Pro
            </h1>
            <p className="text-lg text-muted-foreground">
              System Optimization Tool
            </p>
          </div>
        </div>

        {/* Progress */}
        <div className="w-80 space-y-4">
          <Progress value={progress} className="h-2" />
          <p className="text-sm text-muted-foreground animate-pulse">
            {currentTask}
          </p>
        </div>

        {/* Version */}
        <div className="text-xs text-muted-foreground">
          <p>Version 1.0.0</p>
          <p className="mt-1">Created by sulindvaas & claude & augment</p>
        </div>
      </div>
    </div>
  )
}
