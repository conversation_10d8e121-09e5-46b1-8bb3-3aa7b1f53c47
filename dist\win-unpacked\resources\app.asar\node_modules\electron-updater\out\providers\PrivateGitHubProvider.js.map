{"version": 3, "file": "PrivateGitHubProvider.js", "sourceRoot": "", "sources": ["../../src/providers/PrivateGitHubProvider.ts"], "names": [], "mappings": ";;;AAAA,+DAAwG;AAExG,qCAA8B;AAC9B,6BAA4B;AAE5B,6BAAyB;AACzB,kCAA4D;AAC5D,qDAAqD;AAErD,yCAAgE;AAMhE,MAAa,qBAAsB,SAAQ,mCAA2C;IACpF,YACE,OAAsB,EACL,OAAmB,EACnB,KAAa,EAC9B,cAAsC;QAEtC,KAAK,CAAC,OAAO,EAAE,gBAAgB,EAAE,cAAc,CAAC,CAAA;QAJ/B,YAAO,GAAP,OAAO,CAAY;QACnB,UAAK,GAAL,KAAK,CAAQ;IAIhC,CAAC;IAES,oBAAoB,CAAC,GAAQ,EAAE,OAAoC;QAC3E,MAAM,MAAM,GAAG,KAAK,CAAC,oBAAoB,CAAC,GAAG,EAAE,OAAO,CAAC,CACtD;QAAC,MAAc,CAAC,QAAQ,GAAG,QAAQ,CAAA;QACpC,OAAO,MAAM,CAAA;IACf,CAAC;IAED,KAAK,CAAC,gBAAgB;QACpB,MAAM,iBAAiB,GAAG,IAAI,wCAAiB,EAAE,CAAA;QACjD,MAAM,WAAW,GAAG,IAAA,yBAAkB,EAAC,IAAI,CAAC,qBAAqB,EAAE,CAAC,CAAA;QAEpE,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,CAAA;QACtE,MAAM,KAAK,GAAG,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,KAAK,WAAW,CAAC,CAAA;QACpE,IAAI,KAAK,IAAI,IAAI,EAAE,CAAC;YAClB,+CAA+C;YAC/C,MAAM,IAAA,+BAAQ,EAAC,eAAe,WAAW,mBAAmB,WAAW,CAAC,QAAQ,IAAI,WAAW,CAAC,IAAI,EAAE,EAAE,oCAAoC,CAAC,CAAA;QAC/I,CAAC;QAED,MAAM,GAAG,GAAG,IAAI,SAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;QAC9B,IAAI,MAAW,CAAA;QACf,IAAI,CAAC;YACH,MAAM,GAAG,IAAA,cAAI,EAAC,CAAC,MAAM,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,IAAI,CAAC,gBAAgB,CAAC,0BAA0B,CAAC,EAAE,iBAAiB,CAAC,CAAE,CAAC,CAAA;QACrH,CAAC;QAAC,OAAO,CAAM,EAAE,CAAC;YAChB,IAAI,CAAC,YAAY,gCAAS,IAAI,CAAC,CAAC,UAAU,KAAK,GAAG,EAAE,CAAC;gBACnD,MAAM,IAAA,+BAAQ,EAAC,eAAe,WAAW,qCAAqC,GAAG,MAAM,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,OAAO,EAAE,EAAE,oCAAoC,CAAC,CAAA;YACtJ,CAAC;YACD,MAAM,CAAC,CAAA;QACT,CAAC;QAED,CAAC;QAAC,MAAkC,CAAC,MAAM,GAAG,WAAW,CAAC,MAAM,CAAA;QAChE,OAAO,MAAM,CAAA;IACf,CAAC;IAED,IAAI,wBAAwB;QAC1B,OAAO,IAAI,CAAC,gBAAgB,CAAC,0BAA0B,CAAC,CAAA;IAC1D,CAAC;IAEO,gBAAgB,CAAC,MAAc;QACrC,OAAO;YACL,MAAM;YACN,aAAa,EAAE,SAAS,IAAI,CAAC,KAAK,EAAE;SACrC,CAAA;IACH,CAAC;IAEO,KAAK,CAAC,oBAAoB,CAAC,iBAAoC;QACrE,MAAM,eAAe,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,CAAA;QACpD,IAAI,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAA;QAC5B,IAAI,CAAC,eAAe,EAAE,CAAC;YACrB,QAAQ,GAAG,GAAG,QAAQ,SAAS,CAAA;QACjC,CAAC;QAED,MAAM,GAAG,GAAG,IAAA,qBAAc,EAAC,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,CAAA;QAClD,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,IAAI,CAAC,gBAAgB,CAAC,gCAAgC,CAAC,EAAE,iBAAiB,CAAC,CAAE,CAAC,CAAA;YACtI,IAAI,eAAe,EAAE,CAAC;gBACpB,OAAQ,OAA0C,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,UAAU,CAAC,IAAI,OAAO,CAAC,CAAC,CAAC,CAAA;YAC5F,CAAC;iBAAM,CAAC;gBACN,OAAO,OAAO,CAAA;YAChB,CAAC;QACH,CAAC;QAAC,OAAO,CAAM,EAAE,CAAC;YAChB,MAAM,IAAA,+BAAQ,EAAC,4CAA4C,GAAG,iDAAiD,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,OAAO,EAAE,EAAE,sCAAsC,CAAC,CAAA;QAChL,CAAC;IACH,CAAC;IAED,IAAY,QAAQ;QAClB,OAAO,IAAI,CAAC,qBAAqB,CAAC,UAAU,IAAI,CAAC,OAAO,CAAC,KAAK,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,WAAW,CAAC,CAAA;IACjG,CAAC;IAED,YAAY,CAAC,UAAmC;QAC9C,OAAO,IAAA,sBAAW,EAAC,UAAU,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE;YACtC,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAA;YAC3D,MAAM,KAAK,GAAG,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,IAAI,IAAI,EAAE,CAAC,IAAI,KAAK,IAAI,CAAC,CAAA;YAC1E,IAAI,KAAK,IAAI,IAAI,EAAE,CAAC;gBAClB,MAAM,IAAA,+BAAQ,EAAC,sBAAsB,IAAI,SAAS,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,EAAE,6BAA6B,CAAC,CAAA;YAChI,CAAC;YAED,OAAO;gBACL,GAAG,EAAE,IAAI,SAAG,CAAC,KAAK,CAAC,GAAG,CAAC;gBACvB,IAAI,EAAE,EAAE;aACT,CAAA;QACH,CAAC,CAAC,CAAA;IACJ,CAAC;CACF;AA3FD,sDA2FC", "sourcesContent": ["import { CancellationToken, GithubOptions, HttpError, newError, UpdateInfo } from \"builder-util-runtime\"\nimport { OutgoingHttpHeaders, RequestOptions } from \"http\"\nimport { load } from \"js-yaml\"\nimport * as path from \"path\"\nimport { AppUpdater } from \"../AppUpdater\"\nimport { URL } from \"url\"\nimport { getChannelFilename, newUrlFromBase } from \"../util\"\nimport { BaseGitHubProvider } from \"./GitHubProvider\"\nimport { ResolvedUpdateFileInfo } from \"../types\"\nimport { getFileList, ProviderRuntimeOptions } from \"./Provider\"\n\nexport interface PrivateGitHubUpdateInfo extends UpdateInfo {\n  assets: Array<Asset>\n}\n\nexport class PrivateGitHubProvider extends BaseGitHubProvider<PrivateGitHubUpdateInfo> {\n  constructor(\n    options: GithubOptions,\n    private readonly updater: AppUpdater,\n    private readonly token: string,\n    runtimeOptions: ProviderRuntimeOptions\n  ) {\n    super(options, \"api.github.com\", runtimeOptions)\n  }\n\n  protected createRequestOptions(url: URL, headers?: OutgoingHttpHeaders | null): RequestOptions {\n    const result = super.createRequestOptions(url, headers)\n    ;(result as any).redirect = \"manual\"\n    return result\n  }\n\n  async getLatestVersion(): Promise<PrivateGitHubUpdateInfo> {\n    const cancellationToken = new CancellationToken()\n    const channelFile = getChannelFilename(this.getDefaultChannelName())\n\n    const releaseInfo = await this.getLatestVersionInfo(cancellationToken)\n    const asset = releaseInfo.assets.find(it => it.name === channelFile)\n    if (asset == null) {\n      // html_url must be always, but just to be sure\n      throw newError(`Cannot find ${channelFile} in the release ${releaseInfo.html_url || releaseInfo.name}`, \"ERR_UPDATER_CHANNEL_FILE_NOT_FOUND\")\n    }\n\n    const url = new URL(asset.url)\n    let result: any\n    try {\n      result = load((await this.httpRequest(url, this.configureHeaders(\"application/octet-stream\"), cancellationToken))!)\n    } catch (e: any) {\n      if (e instanceof HttpError && e.statusCode === 404) {\n        throw newError(`Cannot find ${channelFile} in the latest release artifacts (${url}): ${e.stack || e.message}`, \"ERR_UPDATER_CHANNEL_FILE_NOT_FOUND\")\n      }\n      throw e\n    }\n\n    ;(result as PrivateGitHubUpdateInfo).assets = releaseInfo.assets\n    return result\n  }\n\n  get fileExtraDownloadHeaders(): OutgoingHttpHeaders | null {\n    return this.configureHeaders(\"application/octet-stream\")\n  }\n\n  private configureHeaders(accept: string) {\n    return {\n      accept,\n      authorization: `token ${this.token}`,\n    }\n  }\n\n  private async getLatestVersionInfo(cancellationToken: CancellationToken): Promise<ReleaseInfo> {\n    const allowPrerelease = this.updater.allowPrerelease\n    let basePath = this.basePath\n    if (!allowPrerelease) {\n      basePath = `${basePath}/latest`\n    }\n\n    const url = newUrlFromBase(basePath, this.baseUrl)\n    try {\n      const version = JSON.parse((await this.httpRequest(url, this.configureHeaders(\"application/vnd.github.v3+json\"), cancellationToken))!)\n      if (allowPrerelease) {\n        return (version as Array<{ prerelease: boolean }>).find(it => it.prerelease) || version[0]\n      } else {\n        return version\n      }\n    } catch (e: any) {\n      throw newError(`Unable to find latest version on GitHub (${url}), please ensure a production release exists: ${e.stack || e.message}`, \"ERR_UPDATER_LATEST_VERSION_NOT_FOUND\")\n    }\n  }\n\n  private get basePath(): string {\n    return this.computeGithubBasePath(`/repos/${this.options.owner}/${this.options.repo}/releases`)\n  }\n\n  resolveFiles(updateInfo: PrivateGitHubUpdateInfo): Array<ResolvedUpdateFileInfo> {\n    return getFileList(updateInfo).map(it => {\n      const name = path.posix.basename(it.url).replace(/ /g, \"-\")\n      const asset = updateInfo.assets.find(it => it != null && it.name === name)\n      if (asset == null) {\n        throw newError(`Cannot find asset \"${name}\" in: ${JSON.stringify(updateInfo.assets, null, 2)}`, \"ERR_UPDATER_ASSET_NOT_FOUND\")\n      }\n\n      return {\n        url: new URL(asset.url),\n        info: it,\n      }\n    })\n  }\n}\n\ninterface ReleaseInfo {\n  name: string\n  html_url: string\n  assets: Array<Asset>\n}\n\nexport interface Asset {\n  name: string\n  url: string\n}\n"]}