import React from 'react'
import { <PERSON><PERSON> } from './ui/button'
import { Minus, X, Settings } from 'lucide-react'
import { Logo } from './logo'

export function TitleBar() {
  const handleMinimize = () => {
    // @ts-ignore
    window.api?.minimize()
  }

  const handleClose = () => {
    // @ts-ignore
    window.api?.close()
  }

  return (
    <div className="h-8 bg-card border-b border-border flex items-center justify-between px-4 select-none drag-region">
      <div className="flex items-center gap-2">
        <Logo size={20} showText={true} />
      </div>
      
      <div className="flex items-center gap-1 no-drag">
        <Button
          variant="ghost"
          size="icon"
          className="h-6 w-6 hover:bg-muted"
          onClick={handleMinimize}
        >
          <Minus size={12} />
        </Button>
        
        <Button
          variant="ghost"
          size="icon"
          className="h-6 w-6 hover:bg-destructive hover:text-destructive-foreground"
          onClick={handleClose}
        >
          <X size={12} />
        </Button>
      </div>
    </div>
  )
}
