import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card'
import { Button } from '../components/ui/button'
import { Switch } from '../components/ui/switch'
import { Progress } from '../components/ui/progress'
import { <PERSON>lider } from '../components/ui/slider'
import { useToast } from '../components/ui/use-toast'
import {
  Zap,
  Settings,
  Clock,
  Shield,
  Cpu,
  Eye,
  HardDrive,
  Loader2,
  CheckCircle,
  AlertTriangle,
  HelpCircle,
  Gauge
} from 'lucide-react'

interface OptimizationOption {
  id: string
  name: string
  description: string
  icon: React.ReactNode
  enabled: boolean
  category: 'startup' | 'services' | 'visual' | 'performance'
  impact: 'low' | 'medium' | 'high'
  warning?: string
}

export function SystemOptimizer() {
  const { toast } = useToast()
  const [isOptimizing, setIsOptimizing] = useState(false)
  const [progress, setProgress] = useState(0)
  const [lastOptimization, setLastOptimization] = useState<Date | null>(null)
  const [optimizationIntensity, setOptimizationIntensity] = useState(50)
  const [cpuPriority, setCpuPriority] = useState(75)
  const [memoryThreshold, setMemoryThreshold] = useState(80)
  const [showGuide, setShowGuide] = useState(false)
  
  const [optimizationOptions, setOptimizationOptions] = useState<OptimizationOption[]>([
    {
      id: 'disable-startup-programs',
      name: 'Disable Unnecessary Startup Programs',
      description: 'Prevent non-essential programs from starting with Windows',
      icon: <Clock className="h-5 w-5" />,
      enabled: true,
      category: 'startup',
      impact: 'high'
    },
    {
      id: 'optimize-services',
      name: 'Optimize Windows Services',
      description: 'Disable unnecessary Windows services to improve performance',
      icon: <Settings className="h-5 w-5" />,
      enabled: true,
      category: 'services',
      impact: 'medium',
      warning: 'May affect some Windows features'
    },
    {
      id: 'disable-visual-effects',
      name: 'Disable Visual Effects',
      description: 'Turn off animations and visual effects for better performance',
      icon: <Eye className="h-5 w-5" />,
      enabled: false,
      category: 'visual',
      impact: 'medium'
    },
    {
      id: 'optimize-power-settings',
      name: 'Optimize Power Settings',
      description: 'Set power plan to high performance mode',
      icon: <Zap className="h-5 w-5" />,
      enabled: true,
      category: 'performance',
      impact: 'medium'
    },
    {
      id: 'disable-background-apps',
      name: 'Disable Background Apps',
      description: 'Prevent apps from running in the background',
      icon: <Shield className="h-5 w-5" />,
      enabled: false,
      category: 'performance',
      impact: 'high',
      warning: 'May affect app notifications'
    },
    {
      id: 'optimize-memory',
      name: 'Optimize Memory Usage',
      description: 'Clear memory cache and optimize RAM usage',
      icon: <Cpu className="h-5 w-5" />,
      enabled: true,
      category: 'performance',
      impact: 'low'
    },
    {
      id: 'disk-optimization',
      name: 'Disk Optimization',
      description: 'Optimize disk performance and defragmentation settings',
      icon: <HardDrive className="h-5 w-5" />,
      enabled: true,
      category: 'performance',
      impact: 'medium'
    }
  ])

  const toggleOption = (id: string) => {
    setOptimizationOptions(prev => 
      prev.map(option => 
        option.id === id ? { ...option, enabled: !option.enabled } : option
      )
    )
  }

  const startOptimization = async () => {
    const enabledOptions = optimizationOptions.filter(option => option.enabled)
    if (enabledOptions.length === 0) {
      toast({
        title: "No Options Selected",
        description: "Please select at least one optimization option",
        variant: "destructive"
      })
      return
    }

    setIsOptimizing(true)
    setProgress(0)
    
    try {
      for (let i = 0; i < enabledOptions.length; i++) {
        const option = enabledOptions[i]
        setProgress((i / enabledOptions.length) * 100)
        
        try {
          // @ts-ignore
          await window.api?.optimizeWindows({ [option.id]: true })
          await new Promise(resolve => setTimeout(resolve, 1500)) // Simulate work
        } catch (error) {
          console.error(`Failed to apply ${option.name}:`, error)
        }
      }
      
      setProgress(100)
      setLastOptimization(new Date())
      
      toast({
        title: "Optimization Complete",
        description: `Applied ${enabledOptions.length} optimizations successfully`,
      })
      
    } catch (error) {
      toast({
        title: "Optimization Failed",
        description: "Some optimizations could not be applied",
        variant: "destructive"
      })
    } finally {
      setIsOptimizing(false)
      setProgress(0)
    }
  }

  const getImpactColor = (impact: string) => {
    switch (impact) {
      case 'low': return 'text-green-500'
      case 'medium': return 'text-yellow-500'
      case 'high': return 'text-red-500'
      default: return 'text-gray-500'
    }
  }

  const getImpactBadge = (impact: string) => {
    switch (impact) {
      case 'low': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
      case 'medium': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
      case 'high': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
    }
  }

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'startup': return <Clock className="h-4 w-4" />
      case 'services': return <Settings className="h-4 w-4" />
      case 'visual': return <Eye className="h-4 w-4" />
      case 'performance': return <Zap className="h-4 w-4" />
      default: return <Settings className="h-4 w-4" />
    }
  }

  const enabledCount = optimizationOptions.filter(option => option.enabled).length
  const categories = ['startup', 'services', 'visual', 'performance']

  return (
    <div className="flex-1 p-4 lg:p-6 space-y-4 lg:space-y-6 overflow-auto custom-scrollbar pb-20 lg:pb-6">
      {/* Header */}
      <div className="flex items-start justify-between pt-12 lg:pt-0">
        <div className="space-y-2">
          <h1 className="text-2xl lg:text-3xl font-bold">System Optimizer</h1>
          <p className="text-muted-foreground text-sm lg:text-base">
            Optimize Windows settings for better performance
          </p>
        </div>
        <Button
          variant="outline"
          size="icon"
          onClick={() => setShowGuide(true)}
          className="shrink-0"
        >
          <HelpCircle className="h-4 w-4" />
        </Button>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-500" />
              <div>
                <p className="text-2xl font-bold">{enabledCount}</p>
                <p className="text-sm text-muted-foreground">Optimizations Selected</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center gap-2">
              <Zap className="h-5 w-5 text-primary" />
              <div>
                <p className="text-2xl font-bold">{optimizationOptions.length}</p>
                <p className="text-sm text-muted-foreground">Available Options</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center gap-2">
              <Clock className="h-5 w-5 text-primary" />
              <div>
                <p className="text-2xl font-bold">
                  {lastOptimization ? lastOptimization.toLocaleDateString() : 'Never'}
                </p>
                <p className="text-sm text-muted-foreground">Last Optimization</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Action Button */}
      <div className="flex gap-4">
        <Button 
          onClick={startOptimization} 
          disabled={isOptimizing || enabledCount === 0}
          size="lg"
        >
          {isOptimizing ? (
            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
          ) : (
            <Zap className="h-4 w-4 mr-2" />
          )}
          {isOptimizing ? 'Optimizing...' : 'Start Optimization'}
        </Button>
      </div>

      {/* Progress */}
      {isOptimizing && (
        <Card>
          <CardContent className="pt-6">
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Applying optimizations...</span>
                <span>{progress.toFixed(0)}%</span>
              </div>
              <Progress value={progress} />
            </div>
          </CardContent>
        </Card>
      )}

      {/* Optimization Options by Category */}
      {categories.map(category => {
        const categoryOptions = optimizationOptions.filter(option => option.category === category)
        if (categoryOptions.length === 0) return null

        return (
          <Card key={category}>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 capitalize">
                {getCategoryIcon(category)}
                {category} Optimizations
              </CardTitle>
              <CardDescription>
                {category === 'startup' && 'Optimize programs that start with Windows'}
                {category === 'services' && 'Manage Windows background services'}
                {category === 'visual' && 'Adjust visual effects and animations'}
                {category === 'performance' && 'General performance improvements'}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {categoryOptions.map((option) => (
                <div key={option.id} className="flex items-start justify-between p-4 border rounded-lg">
                  <div className="flex items-start gap-3 flex-1">
                    {option.icon}
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <h4 className="font-medium">{option.name}</h4>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getImpactBadge(option.impact)}`}>
                          {option.impact} impact
                        </span>
                      </div>
                      <p className="text-sm text-muted-foreground mb-2">{option.description}</p>
                      {option.warning && (
                        <div className="flex items-center gap-2 text-sm text-yellow-600 dark:text-yellow-400">
                          <AlertTriangle className="h-4 w-4" />
                          <span>{option.warning}</span>
                        </div>
                      )}
                    </div>
                  </div>
                  <Switch
                    checked={option.enabled}
                    onCheckedChange={() => toggleOption(option.id)}
                    disabled={isOptimizing}
                  />
                </div>
              ))}
            </CardContent>
          </Card>
        )
      })}

      {/* Warning */}
      <Card className="border-yellow-200 dark:border-yellow-800">
        <CardContent className="pt-6">
          <div className="flex items-start gap-3">
            <AlertTriangle className="h-5 w-5 text-yellow-500 mt-0.5" />
            <div>
              <h4 className="font-medium text-yellow-800 dark:text-yellow-200 mb-1">
                Important Notice
              </h4>
              <p className="text-sm text-yellow-700 dark:text-yellow-300">
                These optimizations modify Windows system settings. While generally safe, 
                some changes may affect certain features or applications. You can always 
                revert changes through Windows settings if needed.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
