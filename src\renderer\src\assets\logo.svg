<svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background Circle with Gradient -->
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3B82F6;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#1D4ED8;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1E40AF;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="lightningGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FBBF24;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#F59E0B;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#D97706;stop-opacity:1" />
    </linearGradient>
    <filter id="glow">
      <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- Main Circle Background -->
  <circle cx="32" cy="32" r="30" fill="url(#bgGradient)" stroke="#1E40AF" stroke-width="2"/>
  
  <!-- Inner Circle for Depth -->
  <circle cx="32" cy="32" r="24" fill="none" stroke="rgba(255,255,255,0.2)" stroke-width="1"/>
  
  <!-- Lightning Bolt (Main Symbol) -->
  <path d="M28 18 L36 18 L30 32 L38 32 L26 46 L34 32 L26 32 Z" 
        fill="url(#lightningGradient)" 
        filter="url(#glow)"
        stroke="#FBBF24" 
        stroke-width="1"/>
  
  <!-- Speed Lines for Motion Effect -->
  <g opacity="0.6">
    <line x1="12" y1="20" x2="18" y2="20" stroke="#FBBF24" stroke-width="2" stroke-linecap="round"/>
    <line x1="10" y1="26" x2="16" y2="26" stroke="#FBBF24" stroke-width="2" stroke-linecap="round"/>
    <line x1="12" y1="32" x2="18" y2="32" stroke="#FBBF24" stroke-width="2" stroke-linecap="round"/>
    <line x1="10" y1="38" x2="16" y2="38" stroke="#FBBF24" stroke-width="2" stroke-linecap="round"/>
    <line x1="12" y1="44" x2="18" y2="44" stroke="#FBBF24" stroke-width="2" stroke-linecap="round"/>
  </g>
  
  <!-- Turbo Indicator Dots -->
  <g opacity="0.8">
    <circle cx="46" cy="24" r="2" fill="#10B981"/>
    <circle cx="50" cy="28" r="1.5" fill="#10B981"/>
    <circle cx="48" cy="32" r="1" fill="#10B981"/>
    <circle cx="50" cy="36" r="1.5" fill="#10B981"/>
    <circle cx="46" cy="40" r="2" fill="#10B981"/>
  </g>
  
  <!-- Highlight for 3D Effect -->
  <ellipse cx="26" cy="22" rx="8" ry="4" fill="rgba(255,255,255,0.3)" opacity="0.6"/>
</svg>
