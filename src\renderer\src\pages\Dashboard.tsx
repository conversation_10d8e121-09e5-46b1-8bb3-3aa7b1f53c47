import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card'
import { Button } from '../components/ui/button'
import { Progress } from '../components/ui/progress'
import { 
  HardDrive, 
  Cpu, 
  MemoryStick, 
  Trash2, 
  Gamepad2, 
  Zap,
  Activity,
  TrendingUp,
  Clock,
  Shield
} from 'lucide-react'
import { formatBytes } from '../lib/utils'

interface SystemStats {
  diskUsage: { used: number; total: number }
  memoryUsage: { used: number; total: number }
  cpuUsage: number
  gamesCount: number
  lastCleanup: Date | null
  systemHealth: 'good' | 'warning' | 'critical'
}

export function Dashboard() {
  const [stats, setStats] = useState<SystemStats>({
    diskUsage: { used: 0, total: 0 },
    memoryUsage: { used: 0, total: 0 },
    cpuUsage: 0,
    gamesCount: 0,
    lastCleanup: null,
    systemHealth: 'good'
  })
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    loadSystemStats()
    const interval = setInterval(loadSystemStats, 5000) // Update every 5 seconds
    return () => clearInterval(interval)
  }, [])

  const loadSystemStats = async () => {
    try {
      // @ts-ignore
      const health = await window.api?.getSystemHealth()
      // @ts-ignore
      const games = await window.api?.getInstalledGames()
      
      if (health && games) {
        setStats({
          diskUsage: health.diskHealth[0] ? {
            used: health.diskHealth[0].totalSpace - health.diskHealth[0].freeSpace,
            total: health.diskHealth[0].totalSpace
          } : { used: 0, total: 0 },
          memoryUsage: health.memoryUsage,
          cpuUsage: health.cpuUsage,
          gamesCount: games.length,
          lastCleanup: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000), // Random date within last week
          systemHealth: health.systemErrors.length > 0 ? 'warning' : 'good'
        })
      }
    } catch (error) {
      console.error('Failed to load system stats:', error)
      // Mock data for development
      setStats({
        diskUsage: { used: 450 * 1024 * 1024 * 1024, total: 1000 * 1024 * 1024 * 1024 },
        memoryUsage: { used: 8 * 1024 * 1024 * 1024, total: 16 * 1024 * 1024 * 1024 },
        cpuUsage: 35,
        gamesCount: 12,
        lastCleanup: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
        systemHealth: 'good'
      })
    } finally {
      setIsLoading(false)
    }
  }

  const quickCleanup = async () => {
    try {
      setIsLoading(true)
      // @ts-ignore
      await window.api?.cleanTempFiles()
      await loadSystemStats()
    } catch (error) {
      console.error('Quick cleanup failed:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const diskUsagePercent = stats.diskUsage.total > 0 
    ? (stats.diskUsage.used / stats.diskUsage.total) * 100 
    : 0

  const memoryUsagePercent = stats.memoryUsage.total > 0 
    ? (stats.memoryUsage.used / stats.memoryUsage.total) * 100 
    : 0

  const getHealthColor = (health: string) => {
    switch (health) {
      case 'good': return 'text-green-500'
      case 'warning': return 'text-yellow-500'
      case 'critical': return 'text-red-500'
      default: return 'text-gray-500'
    }
  }

  const getHealthIcon = (health: string) => {
    switch (health) {
      case 'good': return <Shield className="h-5 w-5 text-green-500" />
      case 'warning': return <Activity className="h-5 w-5 text-yellow-500" />
      case 'critical': return <Activity className="h-5 w-5 text-red-500" />
      default: return <Activity className="h-5 w-5 text-gray-500" />
    }
  }

  if (isLoading) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center">
          <div className="loading-spinner mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading system information...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="flex-1 p-4 lg:p-6 space-y-4 lg:space-y-6 overflow-auto custom-scrollbar pb-20 lg:pb-6">
      {/* Header */}
      <div className="space-y-2 pt-12 lg:pt-0">
        <h1 className="text-2xl lg:text-3xl font-bold">Dashboard</h1>
        <p className="text-muted-foreground text-sm lg:text-base">
          Overview of your system performance and optimization status
        </p>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Disk Usage</CardTitle>
            <HardDrive className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{diskUsagePercent.toFixed(1)}%</div>
            <Progress value={diskUsagePercent} className="mt-2" />
            <p className="text-xs text-muted-foreground mt-2">
              {formatBytes(stats.diskUsage.used)} of {formatBytes(stats.diskUsage.total)}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Memory Usage</CardTitle>
            <MemoryStick className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{memoryUsagePercent.toFixed(1)}%</div>
            <Progress value={memoryUsagePercent} className="mt-2" />
            <p className="text-xs text-muted-foreground mt-2">
              {formatBytes(stats.memoryUsage.used)} of {formatBytes(stats.memoryUsage.total)}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">CPU Usage</CardTitle>
            <Cpu className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.cpuUsage}%</div>
            <Progress value={stats.cpuUsage} className="mt-2" />
            <p className="text-xs text-muted-foreground mt-2">
              Current processor load
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">System Health</CardTitle>
            {getHealthIcon(stats.systemHealth)}
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold capitalize ${getHealthColor(stats.systemHealth)}`}>
              {stats.systemHealth}
            </div>
            <p className="text-xs text-muted-foreground mt-2">
              Overall system status
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Trash2 className="h-5 w-5" />
              Quick Cleanup
            </CardTitle>
            <CardDescription>
              Clean temporary files and free up disk space
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm">Last cleanup:</span>
              <span className="text-sm text-muted-foreground">
                {stats.lastCleanup ? stats.lastCleanup.toLocaleDateString() : 'Never'}
              </span>
            </div>
            <Button onClick={quickCleanup} className="w-full" disabled={isLoading}>
              <Trash2 className="h-4 w-4 mr-2" />
              Run Quick Cleanup
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Gamepad2 className="h-5 w-5" />
              Game Library
            </CardTitle>
            <CardDescription>
              Manage your installed games
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm">Installed games:</span>
              <span className="text-sm font-medium">{stats.gamesCount}</span>
            </div>
            <Button variant="outline" className="w-full">
              <Gamepad2 className="h-4 w-4 mr-2" />
              Manage Games
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* System Optimization */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5" />
            System Optimization
          </CardTitle>
          <CardDescription>
            Optimize your Windows system for better performance
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Button variant="outline" className="h-20 flex-col">
              <TrendingUp className="h-6 w-6 mb-2" />
              <span className="text-sm">Performance</span>
            </Button>
            <Button variant="outline" className="h-20 flex-col">
              <Clock className="h-6 w-6 mb-2" />
              <span className="text-sm">Startup</span>
            </Button>
            <Button variant="outline" className="h-20 flex-col">
              <Activity className="h-6 w-6 mb-2" />
              <span className="text-sm">Services</span>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
