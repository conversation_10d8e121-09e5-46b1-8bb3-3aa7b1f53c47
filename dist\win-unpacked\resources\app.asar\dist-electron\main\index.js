"use strict";
const electron = require("electron");
const path = require("path");
const child_process = require("child_process");
const util = require("util");
const fs = require("fs/promises");
const os = require("os");
function _interopNamespaceDefault(e) {
  const n = Object.create(null, { [Symbol.toStringTag]: { value: "Module" } });
  if (e) {
    for (const k in e) {
      if (k !== "default") {
        const d = Object.getOwnPropertyDescriptor(e, k);
        Object.defineProperty(n, k, d.get ? d : {
          enumerable: true,
          get: () => e[k]
        });
      }
    }
  }
  n.default = e;
  return Object.freeze(n);
}
const path__namespace = /* @__PURE__ */ _interopNamespaceDefault(path);
const fs__namespace = /* @__PURE__ */ _interopNamespaceDefault(fs);
const os__namespace = /* @__PURE__ */ _interopNamespaceDefault(os);
const execAsync = util.promisify(child_process.exec);
class WindowsSystemAPI {
  async cleanSystemCache() {
    const result = {
      success: true,
      freedSpace: 0,
      errors: [],
      details: []
    };
    try {
      const tempPaths = [
        path__namespace.join(os__namespace.tmpdir()),
        path__namespace.join(process.env.LOCALAPPDATA || "", "Temp"),
        path__namespace.join(process.env.WINDIR || "C:\\Windows", "Temp"),
        path__namespace.join(process.env.WINDIR || "C:\\Windows", "Prefetch")
      ];
      for (const tempPath of tempPaths) {
        try {
          const sizeBefore = await this.getDirectorySize(tempPath);
          await this.cleanDirectory(tempPath);
          const sizeAfter = await this.getDirectorySize(tempPath);
          const freed = sizeBefore - sizeAfter;
          result.freedSpace += freed;
          result.details.push(`Cleaned ${tempPath}: ${this.formatBytes(freed)} freed`);
        } catch (error) {
          result.errors.push(`Failed to clean ${tempPath}: ${error}`);
        }
      }
      await this.cleanBrowserCaches(result);
      try {
        await execAsync("cleanmgr /sagerun:1");
        result.details.push("Windows Disk Cleanup completed");
      } catch (error) {
        result.errors.push(`Disk Cleanup failed: ${error}`);
      }
    } catch (error) {
      result.success = false;
      result.errors.push(`Cache cleaning failed: ${error}`);
    }
    return result;
  }
  async cleanTempFiles() {
    const result = {
      success: true,
      freedSpace: 0,
      errors: [],
      details: []
    };
    try {
      const tempDirs = [
        os__namespace.tmpdir(),
        path__namespace.join(process.env.LOCALAPPDATA || "", "Temp"),
        path__namespace.join(process.env.APPDATA || "", "Local", "Temp")
      ];
      for (const dir of tempDirs) {
        try {
          const sizeBefore = await this.getDirectorySize(dir);
          await this.cleanDirectory(dir, true);
          const sizeAfter = await this.getDirectorySize(dir);
          const freed = sizeBefore - sizeAfter;
          result.freedSpace += freed;
          result.details.push(`Cleaned temp directory ${dir}: ${this.formatBytes(freed)} freed`);
        } catch (error) {
          result.errors.push(`Failed to clean ${dir}: ${error}`);
        }
      }
    } catch (error) {
      result.success = false;
      result.errors.push(`Temp file cleaning failed: ${error}`);
    }
    return result;
  }
  async getInstalledGames() {
    const games = [];
    try {
      const steamGames = await this.getSteamGames();
      games.push(...steamGames);
      const epicGames = await this.getEpicGames();
      games.push(...epicGames);
      const storeGames = await this.getWindowsStoreGames();
      games.push(...storeGames);
      const commonGames = await this.getCommonDirectoryGames();
      games.push(...commonGames);
    } catch (error) {
      console.error("Failed to get installed games:", error);
    }
    return games;
  }
  async launchGame(gamePath) {
    try {
      child_process.spawn(gamePath, [], { detached: true, stdio: "ignore" });
      return true;
    } catch (error) {
      console.error("Failed to launch game:", error);
      return false;
    }
  }
  async getSystemHealth() {
    const health = {
      diskHealth: [],
      memoryUsage: { total: 0, used: 0, available: 0 },
      cpuUsage: 0,
      systemErrors: []
    };
    try {
      health.diskHealth = await this.getDiskHealth();
      health.memoryUsage = await this.getMemoryUsage();
      health.cpuUsage = await this.getCPUUsage();
      health.systemErrors = await this.getSystemErrors();
    } catch (error) {
      console.error("Failed to get system health:", error);
    }
    return health;
  }
  async optimizeWindows(options) {
    const result = {
      success: true,
      freedSpace: 0,
      errors: [],
      details: []
    };
    try {
      if (options.disableStartupPrograms) {
        await this.optimizeStartupPrograms(result);
      }
      if (options.disableUnnecessaryServices) {
        await this.optimizeServices(result);
      }
      if (options.optimizeVisualEffects) {
        await this.optimizeVisualEffects(result);
      }
      if (options.cleanRegistry) {
        await this.cleanRegistry(result);
      }
    } catch (error) {
      result.success = false;
      result.errors.push(`Windows optimization failed: ${error}`);
    }
    return result;
  }
  // Helper methods
  async getDirectorySize(dirPath) {
    try {
      const stats = await fs__namespace.stat(dirPath);
      if (!stats.isDirectory())
        return stats.size;
      let totalSize = 0;
      const items = await fs__namespace.readdir(dirPath);
      for (const item of items) {
        const itemPath = path__namespace.join(dirPath, item);
        try {
          const itemStats = await fs__namespace.stat(itemPath);
          if (itemStats.isDirectory()) {
            totalSize += await this.getDirectorySize(itemPath);
          } else {
            totalSize += itemStats.size;
          }
        } catch (error) {
        }
      }
      return totalSize;
    } catch (error) {
      return 0;
    }
  }
  async cleanDirectory(dirPath, keepStructure = false) {
    try {
      const items = await fs__namespace.readdir(dirPath);
      for (const item of items) {
        const itemPath = path__namespace.join(dirPath, item);
        try {
          const stats = await fs__namespace.stat(itemPath);
          if (stats.isDirectory()) {
            if (keepStructure) {
              await this.cleanDirectory(itemPath, true);
            } else {
              await fs__namespace.rmdir(itemPath, { recursive: true });
            }
          } else {
            await fs__namespace.unlink(itemPath);
          }
        } catch (error) {
        }
      }
    } catch (error) {
    }
  }
  formatBytes(bytes) {
    if (bytes === 0)
      return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB", "TB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  }
  // Additional helper methods would be implemented here...
  async cleanBrowserCaches(result) {
  }
  async getSteamGames() {
    return [];
  }
  async getEpicGames() {
    return [];
  }
  async getWindowsStoreGames() {
    return [];
  }
  async getCommonDirectoryGames() {
    return [];
  }
  async getDiskHealth() {
    return [];
  }
  async getMemoryUsage() {
    return { total: 0, used: 0, available: 0 };
  }
  async getCPUUsage() {
    return 0;
  }
  async getSystemErrors() {
    return [];
  }
  async optimizeStartupPrograms(result) {
  }
  async optimizeServices(result) {
  }
  async optimizeVisualEffects(result) {
  }
  async cleanRegistry(result) {
  }
}
function createWindow() {
  const mainWindow = new electron.BrowserWindow({
    width: 1200,
    height: 800,
    minWidth: 1e3,
    minHeight: 700,
    show: false,
    autoHideMenuBar: true,
    // ...(process.platform === 'linux' ? { icon } : {}),
    webPreferences: {
      preload: path.join(__dirname, "../preload/index.js"),
      sandbox: false,
      contextIsolation: true,
      enableRemoteModule: false,
      nodeIntegration: false
    },
    titleBarStyle: "hidden",
    titleBarOverlay: {
      color: "#1e1e1e",
      symbolColor: "#ffffff"
    }
  });
  mainWindow.on("ready-to-show", () => {
    mainWindow.show();
  });
  mainWindow.webContents.setWindowOpenHandler((details) => {
    electron.shell.openExternal(details.url);
    return { action: "deny" };
  });
  {
    mainWindow.loadFile(path.join(__dirname, "../renderer/index.html"));
  }
}
electron.app.whenReady().then(() => {
  electron.app.setAppUserModelId("com.sulindvaas.winoptimizer");
  createWindow();
  electron.app.on("activate", function() {
    if (electron.BrowserWindow.getAllWindows().length === 0)
      createWindow();
  });
});
electron.app.on("window-all-closed", () => {
  if (process.platform !== "darwin") {
    electron.app.quit();
  }
});
const systemAPI = new WindowsSystemAPI();
electron.ipcMain.handle("system:cleanCache", async () => {
  try {
    return await systemAPI.cleanSystemCache();
  } catch (error) {
    console.error("Cache cleaning failed:", error);
    throw error;
  }
});
electron.ipcMain.handle("system:cleanTempFiles", async () => {
  try {
    return await systemAPI.cleanTempFiles();
  } catch (error) {
    console.error("Temp files cleaning failed:", error);
    throw error;
  }
});
electron.ipcMain.handle("system:getInstalledGames", async () => {
  try {
    return await systemAPI.getInstalledGames();
  } catch (error) {
    console.error("Failed to get installed games:", error);
    throw error;
  }
});
electron.ipcMain.handle("system:launchGame", async (_, gamePath) => {
  try {
    return await systemAPI.launchGame(gamePath);
  } catch (error) {
    console.error("Failed to launch game:", error);
    throw error;
  }
});
electron.ipcMain.handle("system:getSystemHealth", async () => {
  try {
    return await systemAPI.getSystemHealth();
  } catch (error) {
    console.error("Failed to get system health:", error);
    throw error;
  }
});
electron.ipcMain.handle("system:optimizeWindows", async (_, options) => {
  try {
    return await systemAPI.optimizeWindows(options);
  } catch (error) {
    console.error("Windows optimization failed:", error);
    throw error;
  }
});
electron.ipcMain.handle("dialog:showMessageBox", async (_, options) => {
  const result = await electron.dialog.showMessageBox(options);
  return result;
});
