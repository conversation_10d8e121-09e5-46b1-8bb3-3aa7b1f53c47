import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card'
import { Button } from '../components/ui/button'
import { useToast } from '../components/ui/use-toast'
import { UserGuide } from '../components/user-guide'
import { useToolExecution } from '../lib/tool-manager'
import {
  Gamepad2,
  Play,
  Folder,
  HardDrive,
  Clock,
  Search,
  Grid,
  List,
  Loader2,
  HelpCircle
} from 'lucide-react'
import { formatBytes } from '../lib/utils'

interface GameInfo {
  name: string
  path: string
  icon?: string
  size?: number
  lastPlayed?: Date
  platform: 'Steam' | 'Epic' | 'Windows Store' | 'Other'
}

export function GameManager() {
  const { toast } = useToast()
  const gameScanTool = useToolExecution('game-scanner')
  const [games, setGames] = useState<GameInfo[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [searchTerm, setSearchTerm] = useState('')
  const [sortBy, setSortBy] = useState<'name' | 'size' | 'lastPlayed' | 'platform'>('name')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc')
  const [filterPlatform, setFilterPlatform] = useState<string>('all')
  const [showGuide, setShowGuide] = useState(false)

  useEffect(() => {
    loadGames()
  }, [])

  const loadGames = async () => {
    setIsLoading(true)
    try {
      // @ts-ignore
      const installedGames = await window.api?.getInstalledGames()
      
      if (installedGames) {
        setGames(installedGames)
      } else {
        // Enhanced mock data for development with more realistic games
        setGames([
          {
            name: 'Cyberpunk 2077',
            path: 'C:\\Games\\Cyberpunk 2077\\bin\\x64\\Cyberpunk2077.exe',
            size: 70 * 1024 * 1024 * 1024, // 70 GB
            lastPlayed: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
            platform: 'Steam'
          },
          {
            name: 'The Witcher 3: Wild Hunt',
            path: 'C:\\Games\\The Witcher 3\\bin\\x64\\witcher3.exe',
            size: 50 * 1024 * 1024 * 1024, // 50 GB
            lastPlayed: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
            platform: 'Steam'
          },
          {
            name: 'Fortnite',
            path: 'C:\\Games\\Epic Games\\Fortnite\\FortniteGame\\Binaries\\Win64\\FortniteClient-Win64-Shipping.exe',
            size: 30 * 1024 * 1024 * 1024, // 30 GB
            lastPlayed: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
            platform: 'Epic'
          },
          {
            name: 'Valorant',
            path: 'C:\\Riot Games\\VALORANT\\live\\VALORANT.exe',
            size: 25 * 1024 * 1024 * 1024, // 25 GB
            lastPlayed: new Date(Date.now() - 1 * 60 * 60 * 1000), // 1 hour ago
            platform: 'Other'
          },
          {
            name: 'League of Legends',
            path: 'C:\\Riot Games\\League of Legends\\Game\\League of Legends.exe',
            size: 15 * 1024 * 1024 * 1024, // 15 GB
            lastPlayed: new Date(Date.now() - 6 * 60 * 60 * 1000), // 6 hours ago
            platform: 'Other'
          },
          {
            name: 'Counter-Strike 2',
            path: 'C:\\Program Files (x86)\\Steam\\steamapps\\common\\Counter-Strike Global Offensive\\cs2.exe',
            size: 35 * 1024 * 1024 * 1024, // 35 GB
            lastPlayed: new Date(Date.now() - 12 * 60 * 60 * 1000), // 12 hours ago
            platform: 'Steam'
          },
          {
            name: 'Minecraft',
            path: 'C:\\Games\\Minecraft\\minecraft.exe',
            size: 2 * 1024 * 1024 * 1024, // 2 GB
            lastPlayed: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
            platform: 'Other'
          },
          {
            name: 'Grand Theft Auto V',
            path: 'C:\\Program Files\\Rockstar Games\\Grand Theft Auto V\\GTA5.exe',
            size: 95 * 1024 * 1024 * 1024, // 95 GB
            lastPlayed: new Date(Date.now() - 4 * 24 * 60 * 60 * 1000),
            platform: 'Steam'
          },
          {
            name: 'Apex Legends',
            path: 'C:\\Program Files (x86)\\Steam\\steamapps\\common\\Apex Legends\\r5apex.exe',
            size: 75 * 1024 * 1024 * 1024, // 75 GB
            lastPlayed: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
            platform: 'Steam'
          },
          {
            name: 'Rocket League',
            path: 'C:\\Program Files\\Epic Games\\rocketleague\\Binaries\\Win64\\RocketLeague.exe',
            size: 20 * 1024 * 1024 * 1024, // 20 GB
            lastPlayed: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
            platform: 'Epic'
          },
          {
            name: 'Halo Infinite',
            path: 'C:\\XboxGames\\Halo Infinite\\Content\\halo.exe',
            size: 45 * 1024 * 1024 * 1024, // 45 GB
            lastPlayed: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
            platform: 'Windows Store'
          },
          {
            name: 'Genshin Impact',
            path: 'C:\\Program Files\\Genshin Impact\\Genshin Impact Game\\GenshinImpact.exe',
            size: 65 * 1024 * 1024 * 1024, // 65 GB
            lastPlayed: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
            platform: 'Other'
          }
        ])
      }
    } catch (error) {
      console.error('Failed to load games:', error)
      toast({
        title: "Failed to Load Games",
        description: "Could not retrieve installed games",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  const scanForGames = async () => {
    if (!gameScanTool.canExecute) {
      toast({
        title: "Scan Unavailable",
        description: `Please wait ${gameScanTool.remainingTimeFormatted} before scanning again`,
        variant: "destructive"
      })
      return
    }

    if (!gameScanTool.startExecution()) {
      return
    }

    try {
      await loadGames()
      toast({
        title: "Scan Complete",
        description: `Found ${games.length} installed games`,
      })
    } catch (error) {
      toast({
        title: "Scan Failed",
        description: "Failed to scan for games",
        variant: "destructive"
      })
    } finally {
      gameScanTool.finishExecution()
    }
  }

  const launchGame = async (game: GameInfo) => {
    try {
      // @ts-ignore
      const success = await window.api?.launchGame(game.path)
      
      if (success) {
        toast({
          title: "Game Launched",
          description: `${game.name} is starting...`,
        })
        
        // Update last played time
        setGames(prev => prev.map(g => 
          g.path === game.path 
            ? { ...g, lastPlayed: new Date() }
            : g
        ))
      } else {
        throw new Error('Launch failed')
      }
    } catch (error) {
      toast({
        title: "Launch Failed",
        description: `Could not launch ${game.name}`,
        variant: "destructive"
      })
    }
  }

  const openGameFolder = (game: GameInfo) => {
    const folderPath = game.path.substring(0, game.path.lastIndexOf('\\'))
    // @ts-ignore
    window.electron?.shell?.openPath(folderPath)
  }

  const getPlatformColor = (platform: string) => {
    switch (platform) {
      case 'Steam': return 'bg-blue-500'
      case 'Epic': return 'bg-gray-800'
      case 'Windows Store': return 'bg-green-500'
      default: return 'bg-purple-500'
    }
  }

  const filteredAndSortedGames = React.useMemo(() => {
    let filtered = games.filter(game => {
      const matchesSearch = game.name.toLowerCase().includes(searchTerm.toLowerCase())
      const matchesPlatform = filterPlatform === 'all' || game.platform === filterPlatform
      return matchesSearch && matchesPlatform
    })

    // Sort games
    filtered.sort((a, b) => {
      let comparison = 0

      switch (sortBy) {
        case 'name':
          comparison = a.name.localeCompare(b.name)
          break
        case 'size':
          comparison = (a.size || 0) - (b.size || 0)
          break
        case 'lastPlayed':
          const aTime = a.lastPlayed ? a.lastPlayed.getTime() : 0
          const bTime = b.lastPlayed ? b.lastPlayed.getTime() : 0
          comparison = bTime - aTime // Most recent first by default
          break
        case 'platform':
          comparison = a.platform.localeCompare(b.platform)
          break
      }

      return sortOrder === 'asc' ? comparison : -comparison
    })

    return filtered
  }, [games, searchTerm, filterPlatform, sortBy, sortOrder])

  const totalSize = games.reduce((total, game) => total + (game.size || 0), 0)

  if (isLoading) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center">
          <div className="loading-spinner mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading installed games...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="flex-1 p-4 lg:p-6 space-y-4 lg:space-y-6 overflow-auto custom-scrollbar force-scrollbar electron-scrollable pb-20 lg:pb-6">
      {/* Header */}
      <div className="flex items-start justify-between pt-12 lg:pt-0">
        <div className="space-y-2">
          <h1 className="text-2xl lg:text-3xl font-bold">Game Manager</h1>
          <p className="text-muted-foreground text-sm lg:text-base">
            Manage and launch your installed games
          </p>
        </div>
        <Button
          variant="outline"
          size="icon"
          onClick={() => setShowGuide(true)}
          className="shrink-0"
        >
          <HelpCircle className="h-4 w-4" />
        </Button>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center gap-2">
              <Gamepad2 className="h-5 w-5 text-primary" />
              <div>
                <p className="text-2xl font-bold">{games.length}</p>
                <p className="text-sm text-muted-foreground">Installed Games</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center gap-2">
              <HardDrive className="h-5 w-5 text-primary" />
              <div>
                <p className="text-2xl font-bold">{formatBytes(totalSize)}</p>
                <p className="text-sm text-muted-foreground">Total Size</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center gap-2">
              <Clock className="h-5 w-5 text-primary" />
              <div>
                <p className="text-2xl font-bold">
                  {games.filter(g => g.lastPlayed && g.lastPlayed > new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)).length}
                </p>
                <p className="text-sm text-muted-foreground">Played This Week</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Controls */}
      <div className="space-y-4">
        <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
          <div className="flex gap-2">
            <Button
              onClick={scanForGames}
              disabled={gameScanTool.isExecuting || !gameScanTool.canExecute}
              variant="outline"
              className={!gameScanTool.canExecute ? 'tool-cooldown' : ''}
            >
              {gameScanTool.isExecuting ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <Search className="h-4 w-4 mr-2" />
              )}
              {gameScanTool.isExecuting ? 'Scanning...' :
               !gameScanTool.canExecute ? `Wait ${gameScanTool.remainingTimeFormatted}` :
               'Scan for Games'}
            </Button>
          </div>

          <div className="flex gap-2 items-center">
            <div className="relative">
              <Search className="h-4 w-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground" />
              <input
                type="text"
                placeholder="Search games..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 pr-4 py-2 border border-input bg-background rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-ring"
              />
            </div>

            <div className="flex border border-input rounded-md">
              <Button
                variant={viewMode === 'grid' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('grid')}
                className="rounded-r-none"
              >
                <Grid className="h-4 w-4" />
              </Button>
              <Button
                variant={viewMode === 'list' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('list')}
                className="rounded-l-none"
              >
                <List className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>

        {/* Advanced Filters */}
        <div className="flex flex-wrap gap-4 items-center">
          <div className="flex items-center gap-2">
            <span className="text-sm text-muted-foreground">Platform:</span>
            <select
              value={filterPlatform}
              onChange={(e) => setFilterPlatform(e.target.value)}
              className="px-3 py-1 border border-input bg-background rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-ring"
            >
              <option value="all">All Platforms</option>
              <option value="Steam">Steam</option>
              <option value="Epic">Epic Games</option>
              <option value="Windows Store">Windows Store</option>
              <option value="Other">Other</option>
            </select>
          </div>

          <div className="flex items-center gap-2">
            <span className="text-sm text-muted-foreground">Sort by:</span>
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as any)}
              className="px-3 py-1 border border-input bg-background rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-ring"
            >
              <option value="name">Name</option>
              <option value="size">Size</option>
              <option value="lastPlayed">Last Played</option>
              <option value="platform">Platform</option>
            </select>
          </div>

          <Button
            variant="ghost"
            size="sm"
            onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
            className="px-2"
          >
            {sortOrder === 'asc' ? '↑' : '↓'}
          </Button>

          <div className="text-sm text-muted-foreground ml-auto">
            {filteredAndSortedGames.length} of {games.length} games
          </div>
        </div>
      </div>

      {/* Games Grid/List */}
      {filteredAndSortedGames.length === 0 ? (
        <Card>
          <CardContent className="pt-6 text-center">
            <Gamepad2 className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
            <h3 className="text-lg font-medium mb-2">No Games Found</h3>
            <p className="text-muted-foreground mb-4">
              {searchTerm ? 'No games match your search.' : 'No installed games detected.'}
            </p>
            {!searchTerm && (
              <Button
                onClick={scanForGames}
                disabled={gameScanTool.isExecuting || !gameScanTool.canExecute}
                className={!gameScanTool.canExecute ? 'tool-cooldown' : ''}
              >
                {gameScanTool.isExecuting ? (
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <Search className="h-4 w-4 mr-2" />
                )}
                {gameScanTool.isExecuting ? 'Scanning...' : 'Scan for Games'}
              </Button>
            )}
          </CardContent>
        </Card>
      ) : viewMode === 'grid' ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {filteredAndSortedGames.map((game, index) => (
            <Card key={index} className="overflow-hidden">
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <CardTitle className="text-lg">{game.name}</CardTitle>
                    <div className="flex items-center gap-2 mt-1">
                      <span className={`px-2 py-1 rounded-full text-xs text-white ${getPlatformColor(game.platform)}`}>
                        {game.platform}
                      </span>
                    </div>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="text-sm text-muted-foreground space-y-1">
                  {game.size && (
                    <p>Size: {formatBytes(game.size)}</p>
                  )}
                  {game.lastPlayed && (
                    <p>Last played: {game.lastPlayed.toLocaleDateString()}</p>
                  )}
                </div>
                
                <div className="flex gap-2">
                  <Button 
                    onClick={() => launchGame(game)}
                    className="flex-1"
                    size="sm"
                  >
                    <Play className="h-4 w-4 mr-2" />
                    Play
                  </Button>
                  <Button 
                    onClick={() => openGameFolder(game)}
                    variant="outline"
                    size="sm"
                  >
                    <Folder className="h-4 w-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <Card>
          <CardContent className="p-0">
            <div className="divide-y">
              {filteredAndSortedGames.map((game, index) => (
                <div key={index} className="p-4 flex items-center justify-between hover:bg-muted/50">
                  <div className="flex items-center gap-4 flex-1">
                    <Gamepad2 className="h-8 w-8 text-muted-foreground" />
                    <div className="flex-1">
                      <h4 className="font-medium">{game.name}</h4>
                      <div className="flex items-center gap-4 text-sm text-muted-foreground">
                        <span className={`px-2 py-1 rounded-full text-xs text-white ${getPlatformColor(game.platform)}`}>
                          {game.platform}
                        </span>
                        {game.size && <span>{formatBytes(game.size)}</span>}
                        {game.lastPlayed && <span>Last played: {game.lastPlayed.toLocaleDateString()}</span>}
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex gap-2">
                    <Button 
                      onClick={() => launchGame(game)}
                      size="sm"
                    >
                      <Play className="h-4 w-4 mr-2" />
                      Play
                    </Button>
                    <Button 
                      onClick={() => openGameFolder(game)}
                      variant="outline"
                      size="sm"
                    >
                      <Folder className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      <UserGuide
        isOpen={showGuide}
        onClose={() => setShowGuide(false)}
        feature="game-manager"
      />
    </div>
  )
}
