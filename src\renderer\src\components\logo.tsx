import React from 'react'

interface LogoProps {
  size?: number
  className?: string
  showText?: boolean
  variant?: 'default' | 'white' | 'dark'
}

export function Logo({ size = 32, className = '', showText = true, variant = 'default' }: LogoProps) {
  const getColors = () => {
    switch (variant) {
      case 'white':
        return {
          bgGradient: 'url(#bgGradientWhite)',
          lightning: 'url(#lightningGradientWhite)',
          text: 'text-white'
        }
      case 'dark':
        return {
          bgGradient: 'url(#bgGradientDark)',
          lightning: 'url(#lightningGradientDark)',
          text: 'text-gray-900'
        }
      default:
        return {
          bgGradient: 'url(#bgGradient)',
          lightning: 'url(#lightningGradient)',
          text: 'text-foreground'
        }
    }
  }

  const colors = getColors()

  return (
    <div className={`flex items-center gap-3 ${className}`}>
      <svg 
        width={size} 
        height={size} 
        viewBox="0 0 64 64" 
        fill="none" 
        xmlns="http://www.w3.org/2000/svg"
        className="shrink-0"
      >
        <defs>
          {/* Default Gradients */}
          <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style={{stopColor:'#3B82F6', stopOpacity:1}} />
            <stop offset="50%" style={{stopColor:'#1D4ED8', stopOpacity:1}} />
            <stop offset="100%" style={{stopColor:'#1E40AF', stopOpacity:1}} />
          </linearGradient>
          <linearGradient id="lightningGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style={{stopColor:'#FBBF24', stopOpacity:1}} />
            <stop offset="50%" style={{stopColor:'#F59E0B', stopOpacity:1}} />
            <stop offset="100%" style={{stopColor:'#D97706', stopOpacity:1}} />
          </linearGradient>
          
          {/* White Variant Gradients */}
          <linearGradient id="bgGradientWhite" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style={{stopColor:'#F8FAFC', stopOpacity:1}} />
            <stop offset="50%" style={{stopColor:'#E2E8F0', stopOpacity:1}} />
            <stop offset="100%" style={{stopColor:'#CBD5E1', stopOpacity:1}} />
          </linearGradient>
          <linearGradient id="lightningGradientWhite" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style={{stopColor:'#FEF3C7', stopOpacity:1}} />
            <stop offset="50%" style={{stopColor:'#FDE68A', stopOpacity:1}} />
            <stop offset="100%" style={{stopColor:'#FCD34D', stopOpacity:1}} />
          </linearGradient>
          
          {/* Dark Variant Gradients */}
          <linearGradient id="bgGradientDark" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style={{stopColor:'#1F2937', stopOpacity:1}} />
            <stop offset="50%" style={{stopColor:'#111827', stopOpacity:1}} />
            <stop offset="100%" style={{stopColor:'#030712', stopOpacity:1}} />
          </linearGradient>
          <linearGradient id="lightningGradientDark" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style={{stopColor:'#FBBF24', stopOpacity:1}} />
            <stop offset="50%" style={{stopColor:'#F59E0B', stopOpacity:1}} />
            <stop offset="100%" style={{stopColor:'#D97706', stopOpacity:1}} />
          </linearGradient>
          
          <filter id="glow">
            <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
            <feMerge> 
              <feMergeNode in="coloredBlur"/>
              <feMergeNode in="SourceGraphic"/>
            </feMerge>
          </filter>
        </defs>
        
        {/* Main Circle Background */}
        <circle cx="32" cy="32" r="30" fill={colors.bgGradient} stroke="#1E40AF" strokeWidth="2"/>
        
        {/* Inner Circle for Depth */}
        <circle cx="32" cy="32" r="24" fill="none" stroke="rgba(255,255,255,0.2)" strokeWidth="1"/>
        
        {/* Lightning Bolt (Main Symbol) */}
        <path d="M28 18 L36 18 L30 32 L38 32 L26 46 L34 32 L26 32 Z" 
              fill={colors.lightning}
              filter="url(#glow)"
              stroke="#FBBF24" 
              strokeWidth="1"/>
        
        {/* Speed Lines for Motion Effect */}
        <g opacity="0.6">
          <line x1="12" y1="20" x2="18" y2="20" stroke="#FBBF24" strokeWidth="2" strokeLinecap="round"/>
          <line x1="10" y1="26" x2="16" y2="26" stroke="#FBBF24" strokeWidth="2" strokeLinecap="round"/>
          <line x1="12" y1="32" x2="18" y2="32" stroke="#FBBF24" strokeWidth="2" strokeLinecap="round"/>
          <line x1="10" y1="38" x2="16" y2="38" stroke="#FBBF24" strokeWidth="2" strokeLinecap="round"/>
          <line x1="12" y1="44" x2="18" y2="44" stroke="#FBBF24" strokeWidth="2" strokeLinecap="round"/>
        </g>
        
        {/* Turbo Indicator Dots */}
        <g opacity="0.8">
          <circle cx="46" cy="24" r="2" fill="#10B981"/>
          <circle cx="50" cy="28" r="1.5" fill="#10B981"/>
          <circle cx="48" cy="32" r="1" fill="#10B981"/>
          <circle cx="50" cy="36" r="1.5" fill="#10B981"/>
          <circle cx="46" cy="40" r="2" fill="#10B981"/>
        </g>
        
        {/* Highlight for 3D Effect */}
        <ellipse cx="26" cy="22" rx="8" ry="4" fill="rgba(255,255,255,0.3)" opacity="0.6"/>
      </svg>
      
      {showText && (
        <div className="flex flex-col">
          <span className={`font-bold text-lg leading-tight ${colors.text}`}>
            TurboBoost
          </span>
          <span className={`text-xs font-medium opacity-80 ${colors.text}`}>
            PRO
          </span>
        </div>
      )}
    </div>
  )
}
