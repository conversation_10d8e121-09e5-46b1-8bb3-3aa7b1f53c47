import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card'
import { Button } from './ui/button'
import { 
  HelpCircle, 
  ChevronRight, 
  ChevronLeft,
  CheckCircle,
  AlertTriangle,
  Info,
  X
} from 'lucide-react'

interface GuideStep {
  title: string
  description: string
  content: React.ReactNode
  type: 'info' | 'warning' | 'success'
}

interface UserGuideProps {
  isOpen: boolean
  onClose: () => void
  feature: 'cache-cleaner' | 'game-manager' | 'system-optimizer' | 'system-health'
}

export function UserGuide({ isOpen, onClose, feature }: UserGuideProps) {
  const [currentStep, setCurrentStep] = useState(0)

  const getGuideSteps = (): GuideStep[] => {
    switch (feature) {
      case 'cache-cleaner':
        return [
          {
            title: "Welcome to Cache Cleaner",
            description: "Learn how to safely clean your system",
            type: 'info',
            content: (
              <div className="space-y-4">
                <p>Cache Cleaner helps you free up disk space by removing temporary files and cache data.</p>
                <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
                  <h4 className="font-medium mb-2">What gets cleaned:</h4>
                  <ul className="list-disc list-inside space-y-1 text-sm">
                    <li>Browser cache files</li>
                    <li>Windows temporary files</li>
                    <li>System cache data</li>
                    <li>Recycle bin (optional)</li>
                  </ul>
                </div>
              </div>
            )
          },
          {
            title: "Safety First",
            description: "Important safety information",
            type: 'warning',
            content: (
              <div className="space-y-4">
                <div className="bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-lg">
                  <h4 className="font-medium mb-2">⚠️ Before you start:</h4>
                  <ul className="list-disc list-inside space-y-1 text-sm">
                    <li>Close all browsers and applications</li>
                    <li>Save any important work</li>
                    <li>The cleaning process is safe but irreversible</li>
                  </ul>
                </div>
                <p className="text-sm text-muted-foreground">
                  WinOptimizer Pro only removes temporary and cache files. Your personal files, 
                  documents, and installed programs are never touched.
                </p>
              </div>
            )
          },
          {
            title: "How to Use",
            description: "Step-by-step instructions",
            type: 'success',
            content: (
              <div className="space-y-4">
                <div className="space-y-3">
                  <div className="flex items-start gap-3">
                    <div className="w-6 h-6 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-sm font-medium">1</div>
                    <div>
                      <h5 className="font-medium">Scan for Files</h5>
                      <p className="text-sm text-muted-foreground">Click "Scan for Files" to analyze what can be cleaned</p>
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <div className="w-6 h-6 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-sm font-medium">2</div>
                    <div>
                      <h5 className="font-medium">Select Options</h5>
                      <p className="text-sm text-muted-foreground">Choose which types of files to clean</p>
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <div className="w-6 h-6 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-sm font-medium">3</div>
                    <div>
                      <h5 className="font-medium">Start Cleanup</h5>
                      <p className="text-sm text-muted-foreground">Click "Start Cleanup" and wait for completion</p>
                    </div>
                  </div>
                </div>
              </div>
            )
          }
        ]

      case 'game-manager':
        return [
          {
            title: "Game Manager Overview",
            description: "Manage all your games in one place",
            type: 'info',
            content: (
              <div className="space-y-4">
                <p>Game Manager automatically detects games from popular platforms and helps you organize them.</p>
                <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
                  <h4 className="font-medium mb-2">Supported Platforms:</h4>
                  <ul className="list-disc list-inside space-y-1 text-sm">
                    <li>Steam</li>
                    <li>Epic Games Store</li>
                    <li>Windows Store/Xbox</li>
                    <li>Custom installations</li>
                  </ul>
                </div>
              </div>
            )
          },
          {
            title: "Game Detection",
            description: "How games are found",
            type: 'info',
            content: (
              <div className="space-y-4">
                <p>WinOptimizer Pro scans common installation directories to find your games:</p>
                <div className="space-y-2 text-sm">
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span>Steam library folders</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span>Epic Games manifests</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span>Windows Store apps</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span>Common game directories</span>
                  </div>
                </div>
              </div>
            )
          },
          {
            title: "Features & Tips",
            description: "Make the most of Game Manager",
            type: 'success',
            content: (
              <div className="space-y-4">
                <div className="space-y-3">
                  <div>
                    <h5 className="font-medium">🎮 Quick Launch</h5>
                    <p className="text-sm text-muted-foreground">Launch games directly from WinOptimizer Pro</p>
                  </div>
                  <div>
                    <h5 className="font-medium">📁 Open Game Folder</h5>
                    <p className="text-sm text-muted-foreground">Quickly access game installation directories</p>
                  </div>
                  <div>
                    <h5 className="font-medium">🔍 Search & Filter</h5>
                    <p className="text-sm text-muted-foreground">Find games by name, platform, or last played date</p>
                  </div>
                  <div>
                    <h5 className="font-medium">📊 Game Statistics</h5>
                    <p className="text-sm text-muted-foreground">View game sizes and play history</p>
                  </div>
                </div>
              </div>
            )
          }
        ]

      case 'system-optimizer':
        return [
          {
            title: "System Optimizer",
            description: "Boost your Windows performance",
            type: 'info',
            content: (
              <div className="space-y-4">
                <p>System Optimizer helps improve Windows performance by adjusting system settings.</p>
                <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
                  <h4 className="font-medium mb-2">Optimization Areas:</h4>
                  <ul className="list-disc list-inside space-y-1 text-sm">
                    <li>Startup programs</li>
                    <li>Windows services</li>
                    <li>Visual effects</li>
                    <li>Power settings</li>
                  </ul>
                </div>
              </div>
            )
          },
          {
            title: "Important Warning",
            description: "Read before optimizing",
            type: 'warning',
            content: (
              <div className="space-y-4">
                <div className="bg-red-50 dark:bg-red-900/20 p-4 rounded-lg">
                  <h4 className="font-medium mb-2">⚠️ Administrator Required</h4>
                  <p className="text-sm mb-2">System optimization requires administrator privileges to modify Windows settings.</p>
                  <ul className="list-disc list-inside space-y-1 text-sm">
                    <li>Run WinOptimizer Pro as Administrator</li>
                    <li>Create a system restore point first</li>
                    <li>Some changes may require a restart</li>
                  </ul>
                </div>
                <p className="text-sm text-muted-foreground">
                  All optimizations can be reverted through Windows settings if needed.
                </p>
              </div>
            )
          },
          {
            title: "Optimization Guide",
            description: "Safe optimization practices",
            type: 'success',
            content: (
              <div className="space-y-4">
                <div className="space-y-3">
                  <div>
                    <h5 className="font-medium">🟢 Safe (Low Impact)</h5>
                    <p className="text-sm text-muted-foreground">Memory optimization, disk cleanup</p>
                  </div>
                  <div>
                    <h5 className="font-medium">🟡 Moderate (Medium Impact)</h5>
                    <p className="text-sm text-muted-foreground">Startup programs, visual effects</p>
                  </div>
                  <div>
                    <h5 className="font-medium">🔴 Advanced (High Impact)</h5>
                    <p className="text-sm text-muted-foreground">Windows services, background apps</p>
                  </div>
                </div>
                <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
                  <p className="text-sm">
                    💡 <strong>Tip:</strong> Start with low-impact optimizations and test your system 
                    before applying more advanced changes.
                  </p>
                </div>
              </div>
            )
          }
        ]

      case 'system-health':
        return [
          {
            title: "System Health Monitor",
            description: "Keep track of your system's health",
            type: 'info',
            content: (
              <div className="space-y-4">
                <p>System Health provides real-time monitoring of your computer's vital statistics.</p>
                <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
                  <h4 className="font-medium mb-2">Monitored Components:</h4>
                  <ul className="list-disc list-inside space-y-1 text-sm">
                    <li>CPU usage and temperature</li>
                    <li>Memory (RAM) usage</li>
                    <li>Disk health and space</li>
                    <li>System errors and warnings</li>
                  </ul>
                </div>
              </div>
            )
          },
          {
            title: "Health Indicators",
            description: "Understanding the status colors",
            type: 'info',
            content: (
              <div className="space-y-4">
                <div className="space-y-3">
                  <div className="flex items-center gap-3">
                    <CheckCircle className="h-5 w-5 text-green-500" />
                    <div>
                      <h5 className="font-medium text-green-600">Good</h5>
                      <p className="text-sm text-muted-foreground">System is running optimally</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    <AlertTriangle className="h-5 w-5 text-yellow-500" />
                    <div>
                      <h5 className="font-medium text-yellow-600">Warning</h5>
                      <p className="text-sm text-muted-foreground">Minor issues that may affect performance</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    <X className="h-5 w-5 text-red-500" />
                    <div>
                      <h5 className="font-medium text-red-600">Critical</h5>
                      <p className="text-sm text-muted-foreground">Issues requiring immediate attention</p>
                    </div>
                  </div>
                </div>
              </div>
            )
          },
          {
            title: "Monitoring Tips",
            description: "Best practices for system monitoring",
            type: 'success',
            content: (
              <div className="space-y-4">
                <div className="space-y-3">
                  <div>
                    <h5 className="font-medium">📊 Regular Checks</h5>
                    <p className="text-sm text-muted-foreground">Monitor your system weekly for best results</p>
                  </div>
                  <div>
                    <h5 className="font-medium">🌡️ Temperature Monitoring</h5>
                    <p className="text-sm text-muted-foreground">High temperatures can indicate cooling issues</p>
                  </div>
                  <div>
                    <h5 className="font-medium">💾 Disk Space</h5>
                    <p className="text-sm text-muted-foreground">Keep at least 15% free space on your main drive</p>
                  </div>
                  <div>
                    <h5 className="font-medium">🔄 Auto-refresh</h5>
                    <p className="text-sm text-muted-foreground">Data updates automatically every 30 seconds</p>
                  </div>
                </div>
              </div>
            )
          }
        ]

      default:
        return []
    }
  }

  const steps = getGuideSteps()
  const currentStepData = steps[currentStep]

  const getIcon = (type: string) => {
    switch (type) {
      case 'warning': return <AlertTriangle className="h-5 w-5 text-yellow-500" />
      case 'success': return <CheckCircle className="h-5 w-5 text-green-500" />
      default: return <Info className="h-5 w-5 text-blue-500" />
    }
  }

  if (!isOpen || !currentStepData) return null

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <Card className="w-full max-w-2xl max-h-[80vh] overflow-auto">
        <CardHeader className="flex flex-row items-center justify-between">
          <div className="flex items-center gap-3">
            {getIcon(currentStepData.type)}
            <div>
              <CardTitle>{currentStepData.title}</CardTitle>
              <CardDescription>{currentStepData.description}</CardDescription>
            </div>
          </div>
          <Button variant="ghost" size="icon" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </CardHeader>
        
        <CardContent className="space-y-6">
          {currentStepData.content}
          
          <div className="flex items-center justify-between pt-4 border-t">
            <div className="flex items-center gap-2">
              {steps.map((_, index) => (
                <div
                  key={index}
                  className={`w-2 h-2 rounded-full ${
                    index === currentStep ? 'bg-primary' : 'bg-muted'
                  }`}
                />
              ))}
            </div>
            
            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={() => setCurrentStep(Math.max(0, currentStep - 1))}
                disabled={currentStep === 0}
              >
                <ChevronLeft className="h-4 w-4 mr-2" />
                Previous
              </Button>
              
              {currentStep < steps.length - 1 ? (
                <Button onClick={() => setCurrentStep(currentStep + 1)}>
                  Next
                  <ChevronRight className="h-4 w-4 ml-2" />
                </Button>
              ) : (
                <Button onClick={onClose}>
                  Get Started
                  <CheckCircle className="h-4 w-4 ml-2" />
                </Button>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
