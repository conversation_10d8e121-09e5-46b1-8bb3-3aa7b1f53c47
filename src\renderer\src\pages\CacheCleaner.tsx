import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card'
import { Button } from '../components/ui/button'
import { Progress } from '../components/ui/progress'
import { Switch } from '../components/ui/switch'
import { Slider } from '../components/ui/slider'
import { useToast } from '../components/ui/use-toast'
import { UserGuide } from '../components/user-guide'
import { useToolExecution } from '../lib/tool-manager'
import {
  Trash2,
  HardDrive,
  Chrome,
  Firefox,
  Folder,
  CheckCircle,
  AlertCircle,
  Loader2,
  HelpCircle,
  Settings,
  Zap
} from 'lucide-react'
import { formatBytes } from '../lib/utils'

interface CleanupOption {
  id: string
  name: string
  description: string
  icon: React.ReactNode
  enabled: boolean
  estimatedSize: number
}

interface CleanupResult {
  success: boolean
  freedSpace: number
  errors: string[]
  details: string[]
}

export function CacheCleaner() {
  const { toast } = useToast()
  const cacheTool = useToolExecution('cache-cleaner')
  const tempTool = useToolExecution('temp-cleaner')
  const [isScanning, setIsScanning] = useState(false)
  const [isCleaning, setIsCleaning] = useState(false)
  const [progress, setProgress] = useState(0)
  const [lastResult, setLastResult] = useState<CleanupResult | null>(null)
  const [showGuide, setShowGuide] = useState(false)
  const [cleaningIntensity, setCleaningIntensity] = useState(50)
  const [autoScan, setAutoScan] = useState(true)
  const [realTimeUpdates, setRealTimeUpdates] = useState(true)
  
  const [cleanupOptions, setCleanupOptions] = useState<CleanupOption[]>([
    {
      id: 'temp-files',
      name: 'Temporary Files',
      description: 'Windows temporary files and folders',
      icon: <Folder className="h-5 w-5" />,
      enabled: true,
      estimatedSize: 1024 * 1024 * 1024 * 2.5 // 2.5 GB
    },
    {
      id: 'browser-cache',
      name: 'Browser Cache',
      description: 'Chrome, Firefox, Edge cache files',
      icon: <Chrome className="h-5 w-5" />,
      enabled: true,
      estimatedSize: 1024 * 1024 * 1024 * 1.2 // 1.2 GB
    },
    {
      id: 'system-cache',
      name: 'System Cache',
      description: 'Windows system cache and prefetch files',
      icon: <HardDrive className="h-5 w-5" />,
      enabled: true,
      estimatedSize: 1024 * 1024 * 512 // 512 MB
    },
    {
      id: 'recycle-bin',
      name: 'Recycle Bin',
      description: 'Empty the recycle bin',
      icon: <Trash2 className="h-5 w-5" />,
      enabled: false,
      estimatedSize: 1024 * 1024 * 256 // 256 MB
    }
  ])

  // Real-time updates
  useEffect(() => {
    if (!realTimeUpdates) return

    const interval = setInterval(async () => {
      if (!isScanning && !isCleaning) {
        // Update estimated sizes in real-time
        setCleanupOptions(prev => prev.map(option => ({
          ...option,
          estimatedSize: option.estimatedSize + (Math.random() - 0.5) * 1024 * 1024 * 10 // ±10MB variation
        })))
      }
    }, 5000) // Update every 5 seconds

    return () => clearInterval(interval)
  }, [realTimeUpdates, isScanning, isCleaning])

  // Auto-scan on mount
  useEffect(() => {
    if (autoScan) {
      scanForFiles()
    }
  }, [])

  const toggleOption = (id: string) => {
    setCleanupOptions(prev => 
      prev.map(option => 
        option.id === id ? { ...option, enabled: !option.enabled } : option
      )
    )
  }

  const scanForFiles = async () => {
    setIsScanning(true)
    setProgress(0)
    
    try {
      // Simulate scanning progress
      for (let i = 0; i <= 100; i += 10) {
        setProgress(i)
        await new Promise(resolve => setTimeout(resolve, 200))
      }
      
      // Update estimated sizes with more accurate data
      setCleanupOptions(prev => prev.map(option => ({
        ...option,
        estimatedSize: option.estimatedSize + Math.random() * 1024 * 1024 * 100 // Add some randomness
      })))
      
      toast({
        title: "Scan Complete",
        description: "Found files ready for cleanup",
      })
    } catch (error) {
      toast({
        title: "Scan Failed",
        description: "Failed to scan for files",
        variant: "destructive"
      })
    } finally {
      setIsScanning(false)
      setProgress(0)
    }
  }

  const startCleanup = async () => {
    const enabledOptions = cleanupOptions.filter(option => option.enabled)
    if (enabledOptions.length === 0) {
      toast({
        title: "No Options Selected",
        description: "Please select at least one cleanup option",
        variant: "destructive"
      })
      return
    }

    // Check if any tools are on cooldown
    const tempEnabled = enabledOptions.some(opt => opt.id === 'temp-files')
    const cacheEnabled = enabledOptions.some(opt => ['browser-cache', 'system-cache'].includes(opt.id))

    if (tempEnabled && !tempTool.canExecute) {
      toast({
        title: "Temp Cleaner Unavailable",
        description: `Please wait ${tempTool.remainingTimeFormatted} before cleaning temp files again`,
        variant: "destructive"
      })
      return
    }

    if (cacheEnabled && !cacheTool.canExecute) {
      toast({
        title: "Cache Cleaner Unavailable",
        description: `Please wait ${cacheTool.remainingTimeFormatted} before cleaning cache again`,
        variant: "destructive"
      })
      return
    }

    // Start execution for applicable tools
    if (tempEnabled) tempTool.startExecution()
    if (cacheEnabled) cacheTool.startExecution()

    setIsCleaning(true)
    setProgress(0)

    try {
      let totalFreed = 0
      const details: string[] = []
      const errors: string[] = []

      for (let i = 0; i < enabledOptions.length; i++) {
        const option = enabledOptions[i]
        setProgress((i / enabledOptions.length) * 100)
        
        try {
          let result: CleanupResult
          
          switch (option.id) {
            case 'temp-files':
              // @ts-ignore
              result = await window.api?.cleanTempFiles()
              break
            case 'browser-cache':
            case 'system-cache':
              // @ts-ignore
              result = await window.api?.cleanCache()
              break
            default:
              // Mock result for other options
              result = {
                success: true,
                freedSpace: option.estimatedSize * (0.7 + Math.random() * 0.3),
                errors: [],
                details: [`Cleaned ${option.name}`]
              }
          }
          
          if (result) {
            totalFreed += result.freedSpace
            details.push(...result.details)
            errors.push(...result.errors)
          }
        } catch (error) {
          errors.push(`Failed to clean ${option.name}: ${error}`)
        }
        
        await new Promise(resolve => setTimeout(resolve, 1000))
      }
      
      setProgress(100)
      
      const finalResult: CleanupResult = {
        success: errors.length === 0,
        freedSpace: totalFreed,
        errors,
        details
      }
      
      setLastResult(finalResult)
      
      toast({
        title: "Cleanup Complete",
        description: `Freed ${formatBytes(totalFreed)} of disk space`,
      })
      
    } catch (error) {
      toast({
        title: "Cleanup Failed",
        description: "An error occurred during cleanup",
        variant: "destructive"
      })
    } finally {
      // Finish execution for applicable tools
      const tempEnabled = enabledOptions.some(opt => opt.id === 'temp-files')
      const cacheEnabled = enabledOptions.some(opt => ['browser-cache', 'system-cache'].includes(opt.id))

      if (tempEnabled) tempTool.finishExecution()
      if (cacheEnabled) cacheTool.finishExecution()

      setIsCleaning(false)
      setProgress(0)
    }
  }

  const totalEstimatedSize = cleanupOptions
    .filter(option => option.enabled)
    .reduce((total, option) => total + option.estimatedSize, 0)

  return (
    <div className="flex-1 p-4 lg:p-6 space-y-4 lg:space-y-6 overflow-auto custom-scrollbar pb-20 lg:pb-6">
      {/* Header */}
      <div className="flex items-start justify-between pt-12 lg:pt-0">
        <div className="space-y-2">
          <h1 className="text-2xl lg:text-3xl font-bold">Cache Cleaner</h1>
          <p className="text-muted-foreground text-sm lg:text-base">
            Clean temporary files, cache, and free up disk space
          </p>
        </div>
        <Button
          variant="outline"
          size="icon"
          onClick={() => setShowGuide(true)}
          className="shrink-0"
        >
          <HelpCircle className="h-4 w-4" />
        </Button>
      </div>

      {/* Quick Actions */}
      <div className="flex flex-col sm:flex-row gap-4">
        <Button
          onClick={scanForFiles}
          disabled={isScanning || isCleaning}
          variant="outline"
          className="w-full sm:w-auto"
        >
          {isScanning ? (
            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
          ) : (
            <HardDrive className="h-4 w-4 mr-2" />
          )}
          Scan for Files
        </Button>

        <Button
          onClick={startCleanup}
          disabled={isScanning || cacheTool.isExecuting || tempTool.isExecuting || totalEstimatedSize === 0}
          className={`w-full sm:w-auto ${(!cacheTool.canExecute || !tempTool.canExecute) ? 'tool-cooldown' : ''}`}
        >
          {(cacheTool.isExecuting || tempTool.isExecuting) ? (
            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
          ) : (
            <Trash2 className="h-4 w-4 mr-2" />
          )}
          {(cacheTool.isExecuting || tempTool.isExecuting) ? 'Cleaning...' : 'Start Cleanup'}
        </Button>
      </div>

      {/* Progress */}
      {(isScanning || cacheTool.isExecuting || tempTool.isExecuting) && (
        <Card>
          <CardContent className="pt-6">
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>{isScanning ? 'Scanning...' : 'Cleaning...'}</span>
                <span>{progress.toFixed(0)}%</span>
              </div>
              <Progress value={progress} />
            </div>
          </CardContent>
        </Card>
      )}

      {/* Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Cleaning Settings
          </CardTitle>
          <CardDescription>
            Configure cleaning behavior and real-time updates
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <Zap className="h-4 w-4" />
                <span className="font-medium">Cleaning Intensity</span>
              </div>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">
                    {cleaningIntensity < 30 ? 'Conservative' :
                     cleaningIntensity < 70 ? 'Balanced' : 'Aggressive'}
                  </span>
                  <span className="text-sm font-medium">{cleaningIntensity}%</span>
                </div>
                <Slider
                  value={[cleaningIntensity]}
                  onValueChange={(value) => {
                    console.log('Slider value changed:', value[0])
                    setCleaningIntensity(value[0])
                  }}
                  max={100}
                  min={0}
                  step={10}
                  className="w-full"
                />
                <p className="text-xs text-muted-foreground">
                  Higher intensity cleans more files but may take longer
                </p>
              </div>
            </div>

            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <span className="font-medium">Auto-scan on startup</span>
                  <p className="text-sm text-muted-foreground">
                    Automatically scan for files when opening this page
                  </p>
                </div>
                <Switch
                  checked={autoScan}
                  onCheckedChange={setAutoScan}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <span className="font-medium">Real-time updates</span>
                  <p className="text-sm text-muted-foreground">
                    Update file sizes and estimates in real-time
                  </p>
                </div>
                <Switch
                  checked={realTimeUpdates}
                  onCheckedChange={setRealTimeUpdates}
                />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Cleanup Options */}
      <Card>
        <CardHeader>
          <CardTitle>Cleanup Options</CardTitle>
          <CardDescription>
            Select what you want to clean. Estimated space to free: {formatBytes(totalEstimatedSize)}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {cleanupOptions.map((option) => (
            <div key={option.id} className="flex items-center justify-between p-4 border rounded-lg">
              <div className="flex items-center gap-3">
                {option.icon}
                <div>
                  <h4 className="font-medium">{option.name}</h4>
                  <p className="text-sm text-muted-foreground">{option.description}</p>
                </div>
              </div>
              <div className="flex items-center gap-4">
                <span className="text-sm text-muted-foreground">
                  {formatBytes(option.estimatedSize)}
                </span>
                <Switch
                  checked={option.enabled}
                  onCheckedChange={() => toggleOption(option.id)}
                />
              </div>
            </div>
          ))}
        </CardContent>
      </Card>

      {/* Last Cleanup Result */}
      {lastResult && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              {lastResult.success ? (
                <CheckCircle className="h-5 w-5 text-green-500" />
              ) : (
                <AlertCircle className="h-5 w-5 text-yellow-500" />
              )}
              Last Cleanup Result
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h4 className="font-medium mb-2">Summary</h4>
                <p className="text-sm text-muted-foreground">
                  Freed: <span className="font-medium text-foreground">{formatBytes(lastResult.freedSpace)}</span>
                </p>
                <p className="text-sm text-muted-foreground">
                  Status: <span className={`font-medium ${lastResult.success ? 'text-green-500' : 'text-yellow-500'}`}>
                    {lastResult.success ? 'Success' : 'Partial Success'}
                  </span>
                </p>
              </div>
              
              {lastResult.errors.length > 0 && (
                <div>
                  <h4 className="font-medium mb-2 text-yellow-500">Errors</h4>
                  <div className="space-y-1">
                    {lastResult.errors.slice(0, 3).map((error, index) => (
                      <p key={index} className="text-xs text-muted-foreground">{error}</p>
                    ))}
                    {lastResult.errors.length > 3 && (
                      <p className="text-xs text-muted-foreground">
                        +{lastResult.errors.length - 3} more errors
                      </p>
                    )}
                  </div>
                </div>
              )}
            </div>
            
            {lastResult.details.length > 0 && (
              <div>
                <h4 className="font-medium mb-2">Details</h4>
                <div className="space-y-1 max-h-32 overflow-auto custom-scrollbar">
                  {lastResult.details.map((detail, index) => (
                    <p key={index} className="text-xs text-muted-foreground">{detail}</p>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      <UserGuide
        isOpen={showGuide}
        onClose={() => setShowGuide(false)}
        feature="cache-cleaner"
      />
    </div>
  )
}
