{"version": 3, "file": "ElectronAppAdapter.js", "sourceRoot": "", "sources": ["../src/ElectronAppAdapter.ts"], "names": [], "mappings": ";;;AAAA,6BAA4B;AAC5B,6CAAyD;AAEzD,MAAa,kBAAkB;IAC7B,YAA6B,MAAM,OAAO,CAAC,UAAU,CAAC,CAAC,GAAG;QAA7B,QAAG,GAAH,GAAG,CAA0B;IAAG,CAAC;IAE9D,SAAS;QACP,OAAO,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE,CAAA;IAC7B,CAAC;IAED,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,CAAA;IAC9B,CAAC;IAED,IAAI,IAAI;QACN,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,CAAA;IAC3B,CAAC;IAED,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,GAAG,CAAC,UAAU,KAAK,IAAI,CAAA;IACrC,CAAC;IAED,IAAI,mBAAmB;QACrB,OAAO,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,gBAAgB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,EAAE,oBAAoB,CAAC,CAAA;IACtI,CAAC;IAED,IAAI,YAAY;QACd,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,UAAU,CAAC,CAAA;IACrC,CAAC;IAED,IAAI,aAAa;QACf,OAAO,IAAA,2BAAc,GAAE,CAAA;IACzB,CAAC;IAED,IAAI;QACF,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAA;IACjB,CAAC;IAED,QAAQ;QACN,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAA;IACrB,CAAC;IAED,MAAM,CAAC,OAAmC;QACxC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAiB,EAAE,QAAgB,EAAE,EAAE,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAA;IACnF,CAAC;CACF;AA1CD,gDA0CC", "sourcesContent": ["import * as path from \"path\"\nimport { AppAdapter, getAppCacheDir } from \"./AppAdapter\"\n\nexport class ElectronAppAdapter implements AppAdapter {\n  constructor(private readonly app = require(\"electron\").app) {}\n\n  whenReady(): Promise<void> {\n    return this.app.whenReady()\n  }\n\n  get version(): string {\n    return this.app.getVersion()\n  }\n\n  get name(): string {\n    return this.app.getName()\n  }\n\n  get isPackaged(): boolean {\n    return this.app.isPackaged === true\n  }\n\n  get appUpdateConfigPath(): string {\n    return this.isPackaged ? path.join(process.resourcesPath, \"app-update.yml\") : path.join(this.app.getAppPath(), \"dev-app-update.yml\")\n  }\n\n  get userDataPath(): string {\n    return this.app.getPath(\"userData\")\n  }\n\n  get baseCachePath(): string {\n    return getAppCacheDir()\n  }\n\n  quit(): void {\n    this.app.quit()\n  }\n\n  relaunch(): void {\n    this.app.relaunch()\n  }\n\n  onQuit(handler: (exitCode: number) => void): void {\n    this.app.once(\"quit\", (_: Electron.Event, exitCode: number) => handler(exitCode))\n  }\n}\n"]}