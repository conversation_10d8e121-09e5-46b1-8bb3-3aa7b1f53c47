{"name": "@radix-ui/react-primitive", "version": "2.1.3", "license": "MIT", "source": "./src/index.ts", "main": "./dist/index.js", "module": "./dist/index.mjs", "files": ["dist", "README.md"], "sideEffects": false, "dependencies": {"@radix-ui/react-slot": "1.2.3"}, "devDependencies": {"@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "eslint": "^9.18.0", "react": "^19.1.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@repo/builder": "0.0.0", "@repo/typescript-config": "0.0.0", "@repo/eslint-config": "0.0.0"}, "peerDependencies": {"@types/react": "*", "@types/react-dom": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}, "homepage": "https://radix-ui.com/primitives", "repository": {"type": "git", "url": "git+https://github.com/radix-ui/primitives.git"}, "types": "./dist/index.d.ts", "exports": {".": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "require": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}}}}