import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card'
import { Button } from '../components/ui/button'
import { Progress } from '../components/ui/progress'
import { useToast } from '../components/ui/use-toast'
import { 
  Activity, 
  HardDrive, 
  Cpu, 
  MemoryStick,
  Shield,
  AlertTriangle,
  CheckCircle,
  XCircle,
  RefreshCw,
  Thermometer,
  Wifi,
  Battery
} from 'lucide-react'
import { formatBytes } from '../lib/utils'

interface SystemHealth {
  diskHealth: {
    drive: string
    totalSpace: number
    freeSpace: number
    health: 'good' | 'warning' | 'critical'
    temperature?: number
  }[]
  memoryUsage: {
    total: number
    used: number
    available: number
  }
  cpuUsage: number
  cpuTemperature?: number
  systemErrors: string[]
  networkStatus: 'connected' | 'disconnected' | 'limited'
  batteryStatus?: {
    level: number
    charging: boolean
    health: 'good' | 'warning' | 'critical'
  }
  lastCheck: Date
}

export function SystemHealth() {
  const { toast } = useToast()
  const [health, setHealth] = useState<SystemHealth | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isChecking, setIsChecking] = useState(false)

  useEffect(() => {
    loadSystemHealth()
    const interval = setInterval(loadSystemHealth, 30000) // Update every 30 seconds
    return () => clearInterval(interval)
  }, [])

  const loadSystemHealth = async () => {
    try {
      // @ts-ignore
      const systemHealth = await window.api?.getSystemHealth()
      
      if (systemHealth) {
        setHealth({
          ...systemHealth,
          lastCheck: new Date()
        })
      } else {
        // Mock data for development
        setHealth({
          diskHealth: [
            {
              drive: 'C:',
              totalSpace: 1000 * 1024 * 1024 * 1024, // 1TB
              freeSpace: 450 * 1024 * 1024 * 1024, // 450GB
              health: 'good',
              temperature: 35
            },
            {
              drive: 'D:',
              totalSpace: 500 * 1024 * 1024 * 1024, // 500GB
              freeSpace: 200 * 1024 * 1024 * 1024, // 200GB
              health: 'warning',
              temperature: 42
            }
          ],
          memoryUsage: {
            total: 16 * 1024 * 1024 * 1024, // 16GB
            used: 8 * 1024 * 1024 * 1024, // 8GB
            available: 8 * 1024 * 1024 * 1024 // 8GB
          },
          cpuUsage: 25,
          cpuTemperature: 55,
          systemErrors: [
            'Warning: High disk temperature on drive D:',
            'Info: Windows Update available'
          ],
          networkStatus: 'connected',
          batteryStatus: {
            level: 85,
            charging: false,
            health: 'good'
          },
          lastCheck: new Date()
        })
      }
    } catch (error) {
      console.error('Failed to load system health:', error)
      toast({
        title: "Health Check Failed",
        description: "Could not retrieve system health information",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  const runHealthCheck = async () => {
    setIsChecking(true)
    try {
      await loadSystemHealth()
      toast({
        title: "Health Check Complete",
        description: "System health information updated",
      })
    } catch (error) {
      toast({
        title: "Health Check Failed",
        description: "Failed to check system health",
        variant: "destructive"
      })
    } finally {
      setIsChecking(false)
    }
  }

  const getHealthIcon = (health: string) => {
    switch (health) {
      case 'good': return <CheckCircle className="h-5 w-5 text-green-500" />
      case 'warning': return <AlertTriangle className="h-5 w-5 text-yellow-500" />
      case 'critical': return <XCircle className="h-5 w-5 text-red-500" />
      default: return <Activity className="h-5 w-5 text-gray-500" />
    }
  }

  const getHealthColor = (health: string) => {
    switch (health) {
      case 'good': return 'text-green-500'
      case 'warning': return 'text-yellow-500'
      case 'critical': return 'text-red-500'
      default: return 'text-gray-500'
    }
  }

  const getOverallHealth = (): 'good' | 'warning' | 'critical' => {
    if (!health) return 'warning'
    
    const criticalIssues = health.diskHealth.filter(d => d.health === 'critical').length
    const warningIssues = health.diskHealth.filter(d => d.health === 'warning').length + 
                          (health.systemErrors.length > 0 ? 1 : 0)
    
    if (criticalIssues > 0) return 'critical'
    if (warningIssues > 0) return 'warning'
    return 'good'
  }

  if (isLoading) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center">
          <div className="loading-spinner mx-auto mb-4"></div>
          <p className="text-muted-foreground">Checking system health...</p>
        </div>
      </div>
    )
  }

  if (!health) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center">
          <XCircle className="h-12 w-12 mx-auto mb-4 text-red-500" />
          <h3 className="text-lg font-medium mb-2">Health Check Failed</h3>
          <p className="text-muted-foreground mb-4">Could not retrieve system health information</p>
          <Button onClick={loadSystemHealth}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Try Again
          </Button>
        </div>
      </div>
    )
  }

  const overallHealth = getOverallHealth()
  const memoryUsagePercent = (health.memoryUsage.used / health.memoryUsage.total) * 100

  return (
    <div className="flex-1 p-4 lg:p-6 space-y-4 lg:space-y-6 overflow-auto custom-scrollbar force-scrollbar electron-scrollable pb-20 lg:pb-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="space-y-2">
          <h1 className="text-3xl font-bold">System Health</h1>
          <p className="text-muted-foreground">
            Monitor your system's health and performance
          </p>
        </div>
        <Button onClick={runHealthCheck} disabled={isChecking}>
          <RefreshCw className={`h-4 w-4 mr-2 ${isChecking ? 'animate-spin' : ''}`} />
          Check Health
        </Button>
      </div>

      {/* Overall Health */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            {getHealthIcon(overallHealth)}
            Overall System Health
          </CardTitle>
          <CardDescription>
            Last checked: {health.lastCheck.toLocaleString()}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className={`text-2xl font-bold capitalize ${getHealthColor(overallHealth)}`}>
            {overallHealth}
          </div>
          <p className="text-sm text-muted-foreground mt-2">
            {overallHealth === 'good' && 'Your system is running optimally'}
            {overallHealth === 'warning' && 'Some issues detected that may affect performance'}
            {overallHealth === 'critical' && 'Critical issues detected that require attention'}
          </p>
        </CardContent>
      </Card>

      {/* System Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {/* CPU */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">CPU Usage</CardTitle>
            <Cpu className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{health.cpuUsage}%</div>
            <Progress value={health.cpuUsage} className="mt-2" />
            {health.cpuTemperature && (
              <div className="flex items-center gap-1 mt-2 text-sm text-muted-foreground">
                <Thermometer className="h-3 w-3" />
                <span>{health.cpuTemperature}°C</span>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Memory */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Memory Usage</CardTitle>
            <MemoryStick className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{memoryUsagePercent.toFixed(1)}%</div>
            <Progress value={memoryUsagePercent} className="mt-2" />
            <p className="text-xs text-muted-foreground mt-2">
              {formatBytes(health.memoryUsage.used)} / {formatBytes(health.memoryUsage.total)}
            </p>
          </CardContent>
        </Card>

        {/* Network */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Network</CardTitle>
            <Wifi className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold capitalize ${
              health.networkStatus === 'connected' ? 'text-green-500' : 
              health.networkStatus === 'limited' ? 'text-yellow-500' : 'text-red-500'
            }`}>
              {health.networkStatus}
            </div>
            <p className="text-xs text-muted-foreground mt-2">
              Internet connectivity status
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Disk Health */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <HardDrive className="h-5 w-5" />
            Disk Health
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {health.diskHealth.map((disk, index) => {
            const usagePercent = ((disk.totalSpace - disk.freeSpace) / disk.totalSpace) * 100
            return (
              <div key={index} className="p-4 border rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <span className="font-medium">Drive {disk.drive}</span>
                    {getHealthIcon(disk.health)}
                  </div>
                  {disk.temperature && (
                    <div className="flex items-center gap-1 text-sm text-muted-foreground">
                      <Thermometer className="h-3 w-3" />
                      <span>{disk.temperature}°C</span>
                    </div>
                  )}
                </div>
                <Progress value={usagePercent} className="mb-2" />
                <div className="flex justify-between text-sm text-muted-foreground">
                  <span>{formatBytes(disk.totalSpace - disk.freeSpace)} used</span>
                  <span>{formatBytes(disk.freeSpace)} free</span>
                </div>
              </div>
            )
          })}
        </CardContent>
      </Card>

      {/* Battery Status (if available) */}
      {health.batteryStatus && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Battery className="h-5 w-5" />
              Battery Status
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between mb-2">
              <span className="text-2xl font-bold">{health.batteryStatus.level}%</span>
              <div className="flex items-center gap-2">
                {getHealthIcon(health.batteryStatus.health)}
                <span className="text-sm text-muted-foreground">
                  {health.batteryStatus.charging ? 'Charging' : 'Not charging'}
                </span>
              </div>
            </div>
            <Progress value={health.batteryStatus.level} className="mb-2" />
            <p className="text-sm text-muted-foreground">
              Battery health: <span className={getHealthColor(health.batteryStatus.health)}>
                {health.batteryStatus.health}
              </span>
            </p>
          </CardContent>
        </Card>
      )}

      {/* System Errors */}
      {health.systemErrors.length > 0 && (
        <Card className="border-yellow-200 dark:border-yellow-800">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-yellow-800 dark:text-yellow-200">
              <AlertTriangle className="h-5 w-5" />
              System Issues
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {health.systemErrors.map((error, index) => (
                <div key={index} className="flex items-start gap-2 text-sm">
                  <AlertTriangle className="h-4 w-4 text-yellow-500 mt-0.5 flex-shrink-0" />
                  <span className="text-yellow-700 dark:text-yellow-300">{error}</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
