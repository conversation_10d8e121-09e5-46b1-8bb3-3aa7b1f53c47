{"version": 3, "file": "AppAdapter.js", "sourceRoot": "", "sources": ["../src/AppAdapter.ts"], "names": [], "mappings": ";;AAiCA,wCAYC;AA7CD,6BAA4B;AAC5B,2BAA0C;AAgC1C,SAAgB,cAAc;IAC5B,MAAM,OAAO,GAAG,IAAA,YAAU,GAAE,CAAA;IAC5B,0EAA0E;IAC1E,IAAI,MAAc,CAAA;IAClB,IAAI,OAAO,CAAC,QAAQ,KAAK,OAAO,EAAE,CAAC;QACjC,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,SAAS,EAAE,OAAO,CAAC,CAAA;IAChF,CAAC;SAAM,IAAI,OAAO,CAAC,QAAQ,KAAK,QAAQ,EAAE,CAAC;QACzC,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,SAAS,EAAE,QAAQ,CAAC,CAAA;IAClD,CAAC;SAAM,CAAC;QACN,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAA;IACxE,CAAC;IACD,OAAO,MAAM,CAAA;AACf,CAAC", "sourcesContent": ["import * as path from \"path\"\nimport { homedir as getHomedir } from \"os\"\n\nexport interface AppAdapter {\n  readonly version: string\n  readonly name: string\n\n  readonly isPackaged: boolean\n\n  /**\n   * Path to update metadata file.\n   */\n  readonly appUpdateConfigPath: string\n\n  /**\n   * Path to user data directory.\n   */\n  readonly userDataPath: string\n\n  /**\n   * Path to cache directory.\n   */\n  readonly baseCachePath: string\n\n  whenReady(): Promise<void>\n\n  relaunch(): void\n\n  quit(): void\n\n  onQuit(handler: (exitCode: number) => void): void\n}\n\nexport function getAppCacheDir() {\n  const homedir = getHomedir()\n  // https://github.com/electron/electron/issues/1404#issuecomment-*********\n  let result: string\n  if (process.platform === \"win32\") {\n    result = process.env[\"LOCALAPPDATA\"] || path.join(homedir, \"AppData\", \"Local\")\n  } else if (process.platform === \"darwin\") {\n    result = path.join(homedir, \"Library\", \"Caches\")\n  } else {\n    result = process.env[\"XDG_CACHE_HOME\"] || path.join(homedir, \".cache\")\n  }\n  return result\n}\n"]}