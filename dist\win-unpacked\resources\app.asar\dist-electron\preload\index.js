"use strict";
const electron = require("electron");
const api = {
  // System operations
  cleanCache: () => electron.ipcRenderer.invoke("system:cleanCache"),
  cleanTempFiles: () => electron.ipcRenderer.invoke("system:cleanTempFiles"),
  getInstalledGames: () => electron.ipcRenderer.invoke("system:getInstalledGames"),
  launchGame: (gamePath) => electron.ipcRenderer.invoke("system:launchGame", gamePath),
  getSystemHealth: () => electron.ipcRenderer.invoke("system:getSystemHealth"),
  optimizeWindows: (options) => electron.ipcRenderer.invoke("system:optimizeWindows", options),
  // Dialog operations
  showMessageBox: (options) => electron.ipcRenderer.invoke("dialog:showMessageBox", options),
  // Settings
  getSettings: () => electron.ipcRenderer.invoke("settings:get"),
  saveSettings: (settings) => electron.ipcRenderer.invoke("settings:save", settings),
  // App operations
  minimize: () => electron.ipcRenderer.invoke("app:minimize"),
  close: () => electron.ipcRenderer.invoke("app:close"),
  // Progress updates
  onProgress: (callback) => {
    electron.ipcRenderer.on("progress:update", (_, progress) => callback(progress));
  },
  removeProgressListener: () => {
    electron.ipcRenderer.removeAllListeners("progress:update");
  }
};
if (process.contextIsolated) {
  try {
    electron.contextBridge.exposeInMainWorld("api", api);
  } catch (error) {
    console.error(error);
  }
} else {
  window.api = api;
}
