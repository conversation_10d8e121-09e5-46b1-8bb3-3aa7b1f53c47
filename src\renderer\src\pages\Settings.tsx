import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card'
import { Button } from '../components/ui/button'
import { Switch } from '../components/ui/switch'
import { Slider } from '../components/ui/slider'
import { useTheme } from '../components/theme-provider'
import { useToast } from '../components/ui/use-toast'
import {
  Settings as SettingsIcon,
  Moon,
  Sun,
  Monitor,
  Minimize2,
  Power,
  Bell,
  Shield,
  Palette,
  Zap,
  HardDrive,
  Volume2,
  Timer,
  Cpu,
  Search
} from 'lucide-react'

interface AppSettings {
  theme: 'light' | 'dark' | 'system'
  minimizeToTray: boolean
  startWithWindows: boolean
  autoStartCleanup: boolean
  notifications: boolean
  autoUpdates: boolean
  telemetry: boolean
  language: string
  // Performance settings
  cpuUsageLimit: number
  memoryUsageLimit: number
  cleanupInterval: number
  scanDepth: number
  // UI settings
  animationSpeed: number
  notificationVolume: number
}

export function Settings() {
  const { theme, setTheme } = useTheme()
  const { toast } = useToast()
  
  const [settings, setSettings] = useState<AppSettings>({
    theme: 'dark',
    minimizeToTray: true,
    startWithWindows: false,
    autoStartCleanup: false,
    notifications: true,
    autoUpdates: true,
    telemetry: false,
    language: 'en',
    // Performance settings
    cpuUsageLimit: 80,
    memoryUsageLimit: 70,
    cleanupInterval: 7, // days
    scanDepth: 3, // directory levels
    // UI settings
    animationSpeed: 50, // percentage
    notificationVolume: 75 // percentage
  })

  const [isLoading, setIsLoading] = useState(false)

  useEffect(() => {
    loadSettings()
  }, [])

  const loadSettings = async () => {
    try {
      // @ts-ignore
      const savedSettings = await window.api?.getSettings()
      if (savedSettings) {
        setSettings(savedSettings)
      }
    } catch (error) {
      console.error('Failed to load settings:', error)
    }
  }

  const saveSettings = async (newSettings: Partial<AppSettings>) => {
    setIsLoading(true)
    try {
      const updatedSettings = { ...settings, ...newSettings }
      setSettings(updatedSettings)
      
      // @ts-ignore
      await window.api?.saveSettings(updatedSettings)
      
      toast({
        title: "Settings Saved",
        description: "Your preferences have been updated",
      })
    } catch (error) {
      toast({
        title: "Save Failed",
        description: "Could not save settings",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleThemeChange = (newTheme: 'light' | 'dark' | 'system') => {
    setTheme(newTheme)
    saveSettings({ theme: newTheme })
  }

  const resetSettings = async () => {
    const defaultSettings: AppSettings = {
      theme: 'dark',
      minimizeToTray: true,
      startWithWindows: false,
      autoStartCleanup: false,
      notifications: true,
      autoUpdates: true,
      telemetry: false,
      language: 'en',
      // Performance settings
      cpuUsageLimit: 80,
      memoryUsageLimit: 70,
      cleanupInterval: 7, // days
      scanDepth: 3, // directory levels
      // UI settings
      animationSpeed: 50, // percentage
      notificationVolume: 75 // percentage
    }

    await saveSettings(defaultSettings)
    toast({
      title: "Settings Reset",
      description: "All settings have been reset to defaults",
    })
  }

  const exportSettings = () => {
    const dataStr = JSON.stringify(settings, null, 2)
    const dataBlob = new Blob([dataStr], { type: 'application/json' })
    const url = URL.createObjectURL(dataBlob)
    const link = document.createElement('a')
    link.href = url
    link.download = 'winoptimizer-settings.json'
    link.click()
    URL.revokeObjectURL(url)
    
    toast({
      title: "Settings Exported",
      description: "Settings file has been downloaded",
    })
  }

  return (
    <div className="flex-1 p-4 lg:p-6 space-y-4 lg:space-y-6 overflow-auto custom-scrollbar force-scrollbar pb-20 lg:pb-6">
      {/* Header */}
      <div className="space-y-2 pt-12 lg:pt-0">
        <h1 className="text-2xl lg:text-3xl font-bold">Settings</h1>
        <p className="text-muted-foreground text-sm lg:text-base">
          Customize your WinOptimizer Pro experience
        </p>
      </div>

      {/* Appearance */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Palette className="h-5 w-5" />
            Appearance
          </CardTitle>
          <CardDescription>
            Customize the look and feel of the application
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-3">
            <h4 className="font-medium">Theme</h4>
            <div className="grid grid-cols-3 gap-3">
              <Button
                variant={theme === 'light' ? 'default' : 'outline'}
                onClick={() => handleThemeChange('light')}
                className="flex items-center gap-2"
              >
                <Sun className="h-4 w-4" />
                Light
              </Button>
              <Button
                variant={theme === 'dark' ? 'default' : 'outline'}
                onClick={() => handleThemeChange('dark')}
                className="flex items-center gap-2"
              >
                <Moon className="h-4 w-4" />
                Dark
              </Button>
              <Button
                variant={theme === 'system' ? 'default' : 'outline'}
                onClick={() => handleThemeChange('system')}
                className="flex items-center gap-2"
              >
                <Monitor className="h-4 w-4" />
                System
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Application Behavior */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <SettingsIcon className="h-5 w-5" />
            Application Behavior
          </CardTitle>
          <CardDescription>
            Configure how the application behaves
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <div className="flex items-center gap-2">
                <Minimize2 className="h-4 w-4" />
                <span className="font-medium">Minimize to System Tray</span>
              </div>
              <p className="text-sm text-muted-foreground">
                Keep the app running in the background when minimized
              </p>
            </div>
            <Switch
              checked={settings.minimizeToTray}
              onCheckedChange={(checked) => saveSettings({ minimizeToTray: checked })}
              disabled={isLoading}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <div className="flex items-center gap-2">
                <Power className="h-4 w-4" />
                <span className="font-medium">Start with Windows</span>
              </div>
              <p className="text-sm text-muted-foreground">
                Automatically start WinOptimizer Pro when Windows boots
              </p>
            </div>
            <Switch
              checked={settings.startWithWindows}
              onCheckedChange={(checked) => saveSettings({ startWithWindows: checked })}
              disabled={isLoading}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <div className="flex items-center gap-2">
                <Bell className="h-4 w-4" />
                <span className="font-medium">Notifications</span>
              </div>
              <p className="text-sm text-muted-foreground">
                Show notifications for completed operations and updates
              </p>
            </div>
            <Switch
              checked={settings.notifications}
              onCheckedChange={(checked) => saveSettings({ notifications: checked })}
              disabled={isLoading}
            />
          </div>
        </CardContent>
      </Card>

      {/* Automation */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5" />
            Automation
          </CardTitle>
          <CardDescription>
            Configure automatic maintenance tasks
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <div className="flex items-center gap-2">
                <HardDrive className="h-4 w-4" />
                <span className="font-medium">Auto Cleanup</span>
              </div>
              <p className="text-sm text-muted-foreground">
                Automatically run cleanup tasks weekly
              </p>
            </div>
            <Switch
              checked={settings.autoStartCleanup}
              onCheckedChange={(checked) => saveSettings({ autoStartCleanup: checked })}
              disabled={isLoading}
            />
          </div>

          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <Timer className="h-4 w-4" />
              <span className="font-medium">Cleanup Interval</span>
            </div>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Days between automatic cleanups</span>
                <span className="text-sm font-medium">{settings.cleanupInterval} days</span>
              </div>
              <Slider
                value={[settings.cleanupInterval]}
                onValueChange={(value) => saveSettings({ cleanupInterval: value[0] })}
                max={30}
                min={1}
                step={1}
                className="w-full"
                disabled={isLoading}
              />
            </div>
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <div className="flex items-center gap-2">
                <Shield className="h-4 w-4" />
                <span className="font-medium">Auto Updates</span>
              </div>
              <p className="text-sm text-muted-foreground">
                Automatically download and install updates
              </p>
            </div>
            <Switch
              checked={settings.autoUpdates}
              onCheckedChange={(checked) => saveSettings({ autoUpdates: checked })}
              disabled={isLoading}
            />
          </div>
        </CardContent>
      </Card>

      {/* Performance */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Cpu className="h-5 w-5" />
            Performance
          </CardTitle>
          <CardDescription>
            Configure performance and resource usage limits
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <Cpu className="h-4 w-4" />
              <span className="font-medium">CPU Usage Limit</span>
            </div>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Maximum CPU usage during operations</span>
                <span className="text-sm font-medium">{settings.cpuUsageLimit}%</span>
              </div>
              <Slider
                value={[settings.cpuUsageLimit]}
                onValueChange={(value) => saveSettings({ cpuUsageLimit: value[0] })}
                max={100}
                min={10}
                step={5}
                className="w-full"
                disabled={isLoading}
              />
            </div>
          </div>

          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <HardDrive className="h-4 w-4" />
              <span className="font-medium">Memory Usage Limit</span>
            </div>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Maximum memory usage during operations</span>
                <span className="text-sm font-medium">{settings.memoryUsageLimit}%</span>
              </div>
              <Slider
                value={[settings.memoryUsageLimit]}
                onValueChange={(value) => saveSettings({ memoryUsageLimit: value[0] })}
                max={90}
                min={20}
                step={5}
                className="w-full"
                disabled={isLoading}
              />
            </div>
          </div>

          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <Search className="h-4 w-4" />
              <span className="font-medium">Scan Depth</span>
            </div>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Directory levels to scan for games and files</span>
                <span className="text-sm font-medium">{settings.scanDepth} levels</span>
              </div>
              <Slider
                value={[settings.scanDepth]}
                onValueChange={(value) => saveSettings({ scanDepth: value[0] })}
                max={10}
                min={1}
                step={1}
                className="w-full"
                disabled={isLoading}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* User Interface */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Palette className="h-5 w-5" />
            User Interface
          </CardTitle>
          <CardDescription>
            Customize interface behavior and feedback
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <Zap className="h-4 w-4" />
              <span className="font-medium">Animation Speed</span>
            </div>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Speed of UI animations and transitions</span>
                <span className="text-sm font-medium">{settings.animationSpeed}%</span>
              </div>
              <Slider
                value={[settings.animationSpeed]}
                onValueChange={(value) => saveSettings({ animationSpeed: value[0] })}
                max={100}
                min={0}
                step={10}
                className="w-full"
                disabled={isLoading}
              />
            </div>
          </div>

          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <Volume2 className="h-4 w-4" />
              <span className="font-medium">Notification Volume</span>
            </div>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Volume level for notification sounds</span>
                <span className="text-sm font-medium">{settings.notificationVolume}%</span>
              </div>
              <Slider
                value={[settings.notificationVolume]}
                onValueChange={(value) => saveSettings({ notificationVolume: value[0] })}
                max={100}
                min={0}
                step={5}
                className="w-full"
                disabled={isLoading}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Privacy */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Privacy
          </CardTitle>
          <CardDescription>
            Control data collection and privacy settings
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <span className="font-medium">Anonymous Telemetry</span>
              <p className="text-sm text-muted-foreground">
                Help improve the app by sending anonymous usage data
              </p>
            </div>
            <Switch
              checked={settings.telemetry}
              onCheckedChange={(checked) => saveSettings({ telemetry: checked })}
              disabled={isLoading}
            />
          </div>
        </CardContent>
      </Card>

      {/* Advanced */}
      <Card>
        <CardHeader>
          <CardTitle>Advanced</CardTitle>
          <CardDescription>
            Advanced settings and data management
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-4">
            <Button onClick={exportSettings} variant="outline">
              Export Settings
            </Button>
            <Button onClick={resetSettings} variant="outline">
              Reset to Defaults
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* App Info */}
      <Card>
        <CardHeader>
          <CardTitle>Application Information</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2">
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-muted-foreground">Version:</span>
              <span className="ml-2 font-medium">1.0.0</span>
            </div>
            <div>
              <span className="text-muted-foreground">Build:</span>
              <span className="ml-2 font-medium">2024.01.15</span>
            </div>
            <div>
              <span className="text-muted-foreground">Platform:</span>
              <span className="ml-2 font-medium">Windows x64</span>
            </div>
            <div>
              <span className="text-muted-foreground">Node.js:</span>
              <span className="ml-2 font-medium">18.17.0</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
