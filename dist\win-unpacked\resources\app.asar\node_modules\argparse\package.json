{"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "CLI arguments parser. Native port of python's argparse.", "version": "2.0.1", "main": "argparse.js", "files": ["argparse.js", "lib/"], "license": "Python-2.0", "repository": "nodeca/argparse", "devDependencies": {"@babel/eslint-parser": "^7.11.0", "@babel/plugin-syntax-class-properties": "^7.10.4", "eslint": "^7.5.0", "mocha": "^8.0.1", "nyc": "^15.1.0"}}